---
type: "always_apply"
description: "globs:"
---

# OA Framework File Size & Development Standards - Solo + AI

**Authority**: President & CEO, E<PERSON><PERSON><PERSON> Consultancy  
**Status**: MANDATORY ENFORCEMENT  
**Development Context**: Solo Developer + AI Assistant  
**Effective Date**: 2025-09-01  

---

## 📏 **FILE SIZE LIMITS**

> **Important**: These are progressive thresholds. "Target" = best practice, "Critical" = mandatory refactor point.

| Metric | Target | Warning Threshold | Critical Threshold | Action Required |
|--------|---------|-------------------|-------------------|-----------------| 
| **Lines per File** | ≤ 700 | ≤ 1200 | ≤ 2200 | IMMEDIATE REFACTOR |
| **File Size** | ≤ 20KB | ≤ 35KB | ≤ 65KB | IMMEDIATE REFACTOR |
| **AI Context Chunks** | ≤ 6 sections | ≤ 10 sections | ≤ 12 sections | RESTRUCTURE |
| **Logical Sections** | ≤ 5 domains | ≤ 8 domains | ≤ 10 domains | SPLIT REQUIRED |

---

## 🎯 **INDUSTRY CONTEXT**

| Standard | Recommended Lines | OA Framework Approach |
|----------|------------------|----------------------|
| **Clean Code** | 200-400 lines | Target: 700 lines (enterprise balance) |
| **Google Style Guide** | ~500 lines | Warning: 1200 lines (documented) |
| **Microsoft Guidelines** | 300-600 lines | Critical: 2200 lines (absolute maximum) |

---

## 🏗️ **STRUCTURE LIMITS** (Solo + AI Rules)

| Element | Limit | Enforcement | Documentation Requirement |
|---------|-------|-------------|---------------------------|
| **Classes per File** | 1 + 4 helper | FLEXIBLE | Document helper relationships |
| **Interfaces per File** | 1 + 10 related | FLEXIBLE | Group by logical domain |
| **Methods per Class** | 25 | MONITOR | AI-friendly section breaks |
| **Lines per Method** | 100 | MONITOR | Progressive documentation (see below) |
| **Parameters per Method** | 8 | MONITOR | Use object parameters for complex signatures |
| **Nested Levels** | 6 | MONITOR | Early returns, guard clauses |

---

## 📋 **TYPE SYSTEM LIMITS** (Solo + AI Rules)

| Element | Limit | Enforcement | Best Practice |
|---------|-------|-------------|---------------|
| **Types per File** | 35 | FLEXIBLE | Group by logical domain |
| **Properties per Interface** | 25 | MONITOR | Use composition for large interfaces |
| **Union Type Members** | 15 | MONITOR | Document each member's purpose |
| **Generic Type Parameters** | 6 | MONITOR | Use descriptive names beyond T/U/V |

---

## ⚙️ **CONFIGURATION LIMITS** (Solo + AI Rules)

| Element | Limit | Enforcement | Naming Convention |
|---------|-------|-------------|-------------------|
| **Constants per File** | 50 | FLEXIBLE | UPPER_SNAKE_CASE with domain prefixes |
| **Enum Members** | 35 | FLEXIBLE | Document each member |
| **Config Object Properties** | 20 | MONITOR | Descriptive property names |

---

## 📚 **UNIFIED DOCUMENTATION STANDARDS**

### **Progressive Documentation Requirements**

| Method Length | Documentation Required | Examples |
|---------------|----------------------|----------|
| **1-20 lines** | Inline comments for complex logic only | `// Calculate compound interest` |
| **21-50 lines** | JSDoc (purpose, params, returns) | `@param userId - The user identifier` |
| **51-100 lines** | JSDoc + inline explanations | JSDoc + `// Handle edge case: empty array` |
| **100+ lines** | JSDoc + inline + AI context sections | All above + section break comments |

### **AI Context Requirements**

| File Size | AI Context Required |
|-----------|---------------------|
| **700+ lines** | Section headers every 150-200 lines |
| **1000+ lines** | AI navigation comments + file overview |
| **1200+ lines** | Comprehensive AI context + justification |

---

## 🔍 **ENFORCEMENT LEVELS** (Solo + AI Context)

| Level | Lines | Impact | Self-Review Required | Documentation |
|-------|-------|--------|---------------------|---------------|
| **🟢 GREEN** | 1-700 | Optimal development | None | Standard docs |
| **🟡 YELLOW** | 701-1200 | Monitor complexity | Optional | Add AI context if >1000 |
| **🔴 RED** | 1201-2200 | Requires justification | **Mandatory** | ADR + refactor plan |
| **⚫ CRITICAL** | 2200+ | **Immediate refactor** | **Block development** | Emergency refactor only |

---

## 🤖 **AI-SPECIFIC REQUIREMENTS**

### **Mandatory AI Optimization**

| Requirement | Implementation | When Required |
|-------------|----------------|---------------|
| **Section Headers** | Every 150-200 lines | Files >700 lines |
| **AI Context Comments** | Major section boundaries | Files >1000 lines |
| **File Overview** | Purpose, scope, navigation | Files >1200 lines |
| **Complex Logic Explanation** | Inline AI-friendly comments | All complex algorithms |

### **AI-Friendly File Structure Template**

```typescript
/**
 * ============================================================================
 * AI CONTEXT: [ComponentName] - [Primary Responsibility]
 * Purpose: [Brief description of file purpose]
 * Complexity: [Simple/Moderate/Complex] - [Justification if complex]
 * AI Navigation: [N] sections, [N] domains
 * Lines: [Current line count] / [Target limit]
 * ============================================================================
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES
// AI Context: External dependencies and type imports
// ============================================================================

// ============================================================================
// SECTION 2: TYPE DEFINITIONS  
// AI Context: Core interfaces and types for [domain]
// ============================================================================

// ============================================================================
// SECTION 3: CONSTANTS & CONFIGURATION
// AI Context: Configuration constants and default values
// ============================================================================

// ============================================================================
// SECTION 4: MAIN IMPLEMENTATION
// AI Context: Primary business logic for [functionality]
// ============================================================================

// ============================================================================
// SECTION 5: HELPER METHODS
// AI Context: Utility methods supporting main implementation
// ============================================================================

// ============================================================================
// SECTION 6: ERROR HANDLING & VALIDATION
// AI Context: Error handling, validation, and edge cases
// ============================================================================
```

---

## ✅ **LARGE FILE JUSTIFICATION CRITERIA**

### **When 1200+ LOC Files Are Acceptable**

✅ **Single Domain Responsibility**: All code serves one cohesive purpose  
✅ **AI Navigation Optimized**: Clear sections, documentation, logical flow  
✅ **High Cohesion**: Methods work together, shared state makes sense  
✅ **Comprehensive Documentation**: Every complex area explained  
✅ **Natural Boundaries**: Splitting would create artificial divisions  

### **Large File Requirements Checklist** (1200+ LOC)

- [ ] **AI Context Comments**: Each section has AI-friendly descriptions
- [ ] **Progressive Documentation**: Follows documentation standards above
- [ ] **Section Boundaries**: Clear separation between logical areas
- [ ] **Navigation Aids**: File overview comment with structure
- [ ] **Performance Notes**: Any performance considerations documented
- [ ] **ADR Created**: Architectural Decision Record justifying size
- [ ] **Refactor Plan**: Timeline for future optimization

---

## 🚫 **MANDATORY REFACTOR TRIGGERS**

Immediate refactoring required when:

❌ **AI Navigation Failure**: AI struggles to understand file structure  
❌ **Development Velocity Impact**: Finding code takes >2 minutes  
❌ **Multiple Responsibilities**: Unrelated domains in one file  
❌ **2200+ Lines**: Absolute maximum exceeded  
❌ **Maintenance Pain**: Changes require understanding entire file  

---

## 🔧 **AUTOMATED ENFORCEMENT**

### **ESLint Configuration**

```json
{
  "rules": {
    "max-lines": ["warn", 1200],
    "max-lines-per-function": ["warn", 100],
    "complexity": ["warn", 15]
  }
}
```

### **Pre-commit Hooks**

- File size validation
- Documentation density check  
- AI context comment verification
- Section structure validation

---

## 📊 **MONITORING & METRICS**

### **Development Metrics**

| Metric | Target | Warning | Action |
|--------|--------|---------|--------|
| **Avg File Size** | <600 lines | >800 lines | Review architecture |
| **Files >1200 LOC** | <5% | >10% | Refactoring sprint |
| **Documentation Density** | >15% | <10% | Documentation review |

### **Self-Assessment Questions** (Monthly)

1. Can AI effectively navigate and explain my largest files?
2. Are file changes taking longer than expected?
3. Do I need significant context switching to work on files?
4. Would these files be clear to me in 6 months?
5. Could another developer understand with AI assistance?

---

## 🎯 **IMPLEMENTATION GUIDELINES**

### **For New Components**

1. **Design Phase**: Plan section structure, document architecture decisions
2. **Implementation**: Add section headers and AI context as you build  
3. **Review**: Test AI navigation, verify documentation completeness

### **For Existing Large Files**

1. **Assessment**: Measure against standards, identify improvements
2. **Incremental**: Add AI context, improve organization
3. **Strategic**: Refactor only when clear benefits exist

---

## 📋 **NAMING CONVENTIONS**

### **Files and Directories**
- **kebab-case** for directories: `user-management/`
- **PascalCase** for components: `UserProfile.tsx`  
- **kebab-case** for utilities: `api-config.ts`
- **camelCase** for hooks: `useAuthentication.ts`

### **Code Elements**
- **camelCase**: variables, functions (`getUserData`)
- **PascalCase**: classes, components (`UserManager`)  
- **UPPER_SNAKE_CASE**: constants (`MAX_RETRY_COUNT`)
- **Prefixed interfaces**: `IUserProfile`
- **Prefixed types**: `TUserRole`

### **CSS Classes**
- **kebab-case**: `user-profile-container`
- **BEM**: `user-card__title`, `user-card--highlighted`

---

## 🚀 **DEVELOPMENT BEST PRACTICES**

### **Code Quality**
- TypeScript strict mode enabled
- ESLint + Prettier configuration
- Comprehensive error handling
- Performance monitoring
- Security best practices (OWASP guidelines)

### **Testing Standards**
- Minimum 80% code coverage
- Unit tests for business logic
- Integration tests for APIs
- End-to-end tests for critical flows
- Test edge cases and error handling

### **Performance Guidelines**
- Code splitting for large applications
- Lazy loading where appropriate
- Bundle size optimization
- Caching strategies
- Loading state management

---

**Authority**: President & CEO, E.Z. Consultancy  
**Effective**: Immediate implementation for all OA Framework development  
**Review Cycle**: Quarterly assessment based on development effectiveness