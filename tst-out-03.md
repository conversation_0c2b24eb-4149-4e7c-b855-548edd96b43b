npm test -- --testPathPattern="GovernanceRuleOrchestrationManager.test.ts" --verbose --testTimeout=10000

> oa-framework@1.0.0 test
> jest --testPathPattern=GovernanceRuleOrchestrationManager.test.ts --verbose --testTimeout=10000

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts (21.245 s, 64 MB heap size)
  GovernanceRuleOrchestrationManager
    Initialization and Lifecycle
      ✓ should initialize with default configuration (6 ms)
      ✓ should initialize resilient timing components correctly (3 ms)
      ✓ should initialize and shutdown properly (3 ms)
      ✕ should handle initialization errors gracefully (4 ms)
      ✓ should validate service name and version (3 ms)
    Orchestration Configuration
      ✓ should initialize orchestration with valid configuration (3 ms)
      ✓ should reject invalid orchestration configuration (25 ms)
      ✓ should handle configuration validation edge cases (3 ms)
    Workflow Execution
      ✕ should execute workflow successfully (5005 ms)
      ✓ should handle workflow validation errors (5 ms)
      ✓ should handle step execution failures (3 ms)
      ✕ should handle different step types correctly (5005 ms)
      ✕ should handle workflow with parallel executions (5005 ms)
    Multi-Rule Coordination
      ✓ should coordinate multi-rule execution successfully (4 ms)
      ✓ should handle empty rules array (3 ms)
      ✓ should handle invalid coordination strategy (2 ms)
      ✓ should handle different coordination strategies (3 ms)
      ✓ should handle rule execution failures with different failure handling strategies (3 ms)
    Rule Set Orchestration
      ✓ should orchestrate rule sets successfully (4 ms)
      ✓ should handle circular dependencies in rule sets (10 ms)
      ✓ should handle empty rule sets (2 ms)
      ✓ should handle rule sets with complex dependencies (3 ms)
    Workflow Coordination
      ✓ should manage workflow coordination successfully (3 ms)
      ✓ should handle workflow validation errors (2 ms)
      ✓ should handle empty workflows array (3 ms)
      ✓ should handle workflow dependencies correctly (2 ms)
    Metrics and Monitoring
      ✓ should get orchestration metrics successfully (3 ms)
      ✓ should handle metrics collection errors (6 ms)
      ✓ should update health status based on metrics (3 ms)
      ✓ should perform health checks on services and orchestrations (2 ms)
    Memory Safety and Resilient Timing
      ✓ should validate MEM-SAFE-002 compliance (2 ms)
      ✕ should handle resilient timing context creation and cleanup (3 ms)
      ✕ should handle timing context errors gracefully (3 ms)
      ✕ should cleanup resources properly during shutdown (3 ms)
    Singleton Factory
      ✓ should return same instance on multiple calls (2 ms)
      ✓ should create new instance with configuration (2 ms)
    Branch Coverage Enhancement
      ✕ should test constructor test mode detection branches (3 ms)
      ✓ should test doInitialize test mode branches (2 ms)
      ✕ should test doShutdown test mode branches (4 ms)
      ✕ should test resilient timing cleanup branches (2 ms)
      ✕ should test resilient timing cleanup error branches (2 ms)
      ✓ should test monitoring setup branches in test mode (3 ms)
      ✕ should test orchestration configuration validation branches (2 ms)
      ✓ should test workflow validation edge cases (3 ms)
      ✕ should test private method branches with surgical precision (3 ms)
      ✕ should test workflow step execution branches (5004 ms)
      ✓ should test error handling branches with strategic error injection (7 ms)
    Surgical Precision Coverage
      ✓ should test conditional branches in private methods (6 ms)
      ✓ should test ternary operator branches (5 ms)
      ✓ should test logical operator branches (&&, ||) (4 ms)
      ✓ should test switch statement branches (4 ms)
      ✓ should test error instanceof branches (5 ms)
    Error Handling and Edge Cases
      ✕ should handle initialization failure gracefully (3 ms)
      ✕ should handle shutdown errors gracefully (3 ms)
      ✓ should handle workflow failure scenarios (4 ms)
      ✓ should handle condition evaluation edge cases (3 ms)
      ✓ should handle resource calculation edge cases (4 ms)
      ✕ should handle metrics collection errors (2 ms)

  ● GovernanceRuleOrchestrationManager › Initialization and Lifecycle › should handle initialization errors gracefully

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: undefined

      304 |       (prodManager as any).doInitialize = jest.fn().mockRejectedValue(new Error('Init failed'));
      305 |
    > 306 |       await expect(prodManager.initialize()).rejects.toThrow('Init failed');
          |             ^
      307 |
      308 |       // Restore original method
      309 |       (prodManager as any).doInitialize = originalDoInitialize;

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:306:13)

  ● GovernanceRuleOrchestrationManager › Workflow Execution › should execute workflow successfully

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      531 |     };
      532 |
    > 533 |     test('should execute workflow successfully', async () => {
          |     ^
      534 |       await manager.initialize();
      535 |
      536 |       const result = await manager.executeWorkflow(sampleWorkflow, sampleContext);

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:533:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:440:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Workflow Execution › should handle different step types correctly

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      571 |     });
      572 |
    > 573 |     test('should handle different step types correctly', async () => {
          |     ^
      574 |       await manager.initialize();
      575 |
      576 |       const multiStepWorkflow = {

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:573:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:440:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Workflow Execution › should handle workflow with parallel executions

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      622 |     });
      623 |
    > 624 |     test('should handle workflow with parallel executions', async () => {
          |     ^
      625 |       await manager.initialize();
      626 |
      627 |       const parallelWorkflow = {

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:624:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:440:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Memory Safety and Resilient Timing › should handle resilient timing context creation and cleanup

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

      1388 |
      1389 |       // Test timing context creation
    > 1390 |       expect(mockResilientTimer.start).toHaveBeenCalled();
           |                                        ^
      1391 |
      1392 |       // Test metrics recording
      1393 |       expect(mockMetricsCollector.recordTiming).toHaveBeenCalled();

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1390:40)

  ● GovernanceRuleOrchestrationManager › Memory Safety and Resilient Timing › should handle timing context errors gracefully

    expect(received).resolves.not.toThrow()

    Received promise rejected instead of resolved
    Rejected to value: [Error: Timing context error]

      1444 |       };
      1445 |
    > 1446 |       await expect(manager.initializeOrchestration(config)).resolves.not.toThrow();
           |             ^
      1447 |     });
      1448 |
      1449 |     test('should cleanup resources properly during shutdown', async () => {

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1446:13)

  ● GovernanceRuleOrchestrationManager › Memory Safety and Resilient Timing › should cleanup resources properly during shutdown

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      1477 |
      1478 |       // Verify cleanup
    > 1479 |       expect((manager as any)._activeOrchestrations.size).toBe(0);
           |                                                           ^
      1480 |       expect((manager as any)._workflowRegistry.size).toBe(0);
      1481 |       expect((manager as any)._coordinationStrategies.size).toBe(0);
      1482 |       expect((manager as any)._serviceRegistry.size).toBe(0);

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1479:59)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test constructor test mode detection branches

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      1538 |         _testMode: false
      1539 |       } as any);
    > 1540 |       expect((managerWithExplicitFalse as any)._testMode).toBe(true); // Should still be true due to Jest
           |                                                           ^
      1541 |     });
      1542 |
      1543 |     test('should test doInitialize test mode branches', async () => {

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1540:59)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test doShutdown test mode branches

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      1567 |
      1568 |       // Verify test mode immediate cleanup
    > 1569 |       expect((manager as any)._activeOrchestrations.size).toBe(0);
           |                                                           ^
      1570 |       expect((manager as any)._workflowRegistry.size).toBe(0);
      1571 |       expect((manager as any)._isInitialized).toBe(false);
      1572 |     });

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1569:59)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test resilient timing cleanup branches

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

      1600 |
      1601 |       // Verify cleanup was called
    > 1602 |       expect(mockTimerWithCleanup.cleanup).toHaveBeenCalled();
           |                                            ^
      1603 |       expect(mockCollectorWithCleanup.cleanup).toHaveBeenCalled();
      1604 |     });
      1605 |

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1602:44)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test resilient timing cleanup error branches

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

      1632 |       // Should not throw, just log error
      1633 |       await expect(prodManager.shutdown()).resolves.not.toThrow();
    > 1634 |       expect(mockTimerWithError.cleanup).toHaveBeenCalled();
           |                                          ^
      1635 |     });
      1636 |
      1637 |     test('should test monitoring setup branches in test mode', async () => {

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1634:42)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test orchestration configuration validation branches

    expect(received).resolves.not.toThrow()

    Received promise rejected instead of resolved
    Rejected to value: [Error: Invalid orchestration configuration: Invalid priority value]

      1699 |       };
      1700 |
    > 1701 |       await expect(manager.initializeOrchestration(validConfig)).resolves.not.toThrow();
           |             ^
      1702 |
      1703 |       // Test invalid mode branch - should validate even in test mode
      1704 |       const invalidModeConfig = { ...validConfig, mode: 'INVALID' as any };

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1701:13)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test private method branches with surgical precision

    expect(received).toThrow()

    Received function did not throw

      1795 |
      1796 |       // Test missing required fields
    > 1797 |       expect(() => validateConfig({})).toThrow();
           |                                        ^
      1798 |       expect(() => validateConfig({ mode: 'sequential' })).toThrow();
      1799 |
      1800 |       // Test invalid enum values

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1797:40)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test workflow step execution branches

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1805 |     });
      1806 |
    > 1807 |     test('should test workflow step execution branches', async () => {
           |     ^
      1808 |       await manager.initialize();
      1809 |
      1810 |       // Test _executeStep with different step types

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1807:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1527:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Error Handling and Edge Cases › should handle initialization failure gracefully

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: undefined

      2079 |       (manager as any).doInitialize = jest.fn().mockRejectedValue(new Error('Initialization failed'));
      2080 |
    > 2081 |       await expect(manager.initialize()).rejects.toThrow('Initialization failed');
           |             ^
      2082 |
      2083 |       // Restore original method
      2084 |       (manager as any).doInitialize = originalDoInitialize;

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:2081:13)

  ● GovernanceRuleOrchestrationManager › Error Handling and Edge Cases › should handle shutdown errors gracefully

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: undefined

      2091 |       (manager as any)._cancelActiveOrchestrations = jest.fn().mockRejectedValue(new Error('Shutdown error'));
      2092 |
    > 2093 |       await expect(manager.shutdown()).rejects.toThrow('Shutdown error');
           |             ^
      2094 |
      2095 |       // Restore original method
      2096 |       (manager as any)._cancelActiveOrchestrations = originalCancelActiveOrchestrations;

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:2093:13)

  ● GovernanceRuleOrchestrationManager › Error Handling and Edge Cases › should handle metrics collection errors

    Metrics error

      2211 |       // Mock performance metrics collection to fail
      2212 |       const originalCollectPerformanceMetrics = (manager as any)._collectPerformanceMetrics;
    > 2213 |       (manager as any)._collectPerformanceMetrics = jest.fn().mockRejectedValue(new Error('Metrics error'));
           |                                                                                 ^
      2214 |
      2215 |       // Should not throw error, just log it
      2216 |       await (manager as any)._collectPerformanceMetrics();

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:2213:81)

Test Suites: 1 failed, 1 total
Tests:       17 failed, 41 passed, 58 total
Snapshots:   0 total
Time:        21.512 s
Ran all test suites matching /GovernanceRuleOrchestrationManager.test.ts/i.
