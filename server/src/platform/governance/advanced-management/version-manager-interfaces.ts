/**
 * ============================================================================
 * OA FRAMEWORK - GOVERNANCE RULE VERSION MANAGER INTERFACES
 * ============================================================================
 * 
 * Version: 1.0.0
 * Created: 2025-01-01
 * Author: OA Framework Development Team
 * 
 * Purpose: Core interfaces for governance rule version management system
 * providing comprehensive version control, backward compatibility validation,
 * and version conflict resolution capabilities.
 * 
 * Compliance:
 * - Anti-Simplification Policy: Complete interface definitions without shortcuts
 * - MEM-SAFE-002: Memory safety integration requirements
 * - OA Framework Standards: Interface naming conventions (I prefix)
 * - Enterprise Grade: Production-ready interface specifications
 * 
 * Authority: docs/core/development-standards.md (Version Manager v2.0)
 * ============================================================================
 */

// ============================================================================
// CORE IMPORTS
// ============================================================================

import {
  TValidationResult
} from '../../../../../shared/src/types/platform/tracking/tracking-types';

import {
  IGovernanceService
} from '../../../../../shared/src/types/platform/governance/governance-interfaces';

// ============================================================================
// VERSION MANAGER CORE INTERFACE
// ============================================================================

/**
 * Version Manager Interface
 * Core interface for governance rule version management operations
 * 
 * Provides comprehensive version control capabilities including:
 * - Rule versioning and lifecycle management
 * - Backward compatibility validation and enforcement
 * - Version conflict detection and resolution
 * - Performance optimization for enterprise-scale operations
 */
export interface IVersionManager extends IGovernanceService {
  // ============================================================================
  // VERSION LIFECYCLE MANAGEMENT
  // ============================================================================

  /**
   * Create new version of a governance rule
   * @param ruleId - Rule identifier
   * @param versionData - Version creation data
   * @param options - Version creation options
   * @returns Promise resolving to version identifier
   */
  createVersion(
    ruleId: string,
    versionData: IVersionCreationData,
    options?: IVersionCreationOptions
  ): Promise<string>;

  /**
   * Update existing version
   * @param versionId - Version identifier
   * @param updateData - Version update data
   * @param options - Update options
   * @returns Promise resolving to validation result
   */
  updateVersion(
    versionId: string,
    updateData: IVersionUpdateData,
    options?: IVersionUpdateOptions
  ): Promise<TValidationResult>;

  /**
   * Delete version (with safety checks)
   * @param versionId - Version identifier
   * @param options - Deletion options
   * @returns Promise resolving to validation result
   */
  deleteVersion(
    versionId: string,
    options?: IVersionDeletionOptions
  ): Promise<TValidationResult>;

  /**
   * Get version details
   * @param versionId - Version identifier
   * @returns Promise resolving to version data or null
   */
  getVersion(versionId: string): Promise<IVersionData | null>;

  /**
   * List versions for a rule
   * @param ruleId - Rule identifier
   * @param filter - Optional version filter
   * @returns Promise resolving to version list
   */
  listVersions(
    ruleId: string,
    filter?: IVersionFilter
  ): Promise<IVersionData[]>;

  // ============================================================================
  // VERSION CONTROL OPERATIONS
  // ============================================================================

  /**
   * Create version branch
   * @param sourceVersionId - Source version identifier
   * @param branchData - Branch creation data
   * @returns Promise resolving to branch identifier
   */
  createBranch(
    sourceVersionId: string,
    branchData: IBranchCreationData
  ): Promise<string>;

  /**
   * Merge version branches
   * @param targetVersionId - Target version identifier
   * @param sourceVersionId - Source version identifier
   * @param mergeOptions - Merge configuration options
   * @returns Promise resolving to merge result
   */
  mergeBranches(
    targetVersionId: string,
    sourceVersionId: string,
    mergeOptions?: IMergeOptions
  ): Promise<IMergeResult>;

  /**
   * Create version tag
   * @param versionId - Version identifier
   * @param tagData - Tag creation data
   * @returns Promise resolving to tag identifier
   */
  createTag(
    versionId: string,
    tagData: ITagCreationData
  ): Promise<string>;

  // ============================================================================
  // BACKWARD COMPATIBILITY MANAGEMENT
  // ============================================================================

  /**
   * Validate backward compatibility
   * @param newVersionId - New version identifier
   * @param baseVersionId - Base version for comparison
   * @returns Promise resolving to compatibility result
   */
  validateBackwardCompatibility(
    newVersionId: string,
    baseVersionId: string
  ): Promise<ICompatibilityResult>;

  /**
   * Generate migration path
   * @param fromVersionId - Source version identifier
   * @param toVersionId - Target version identifier
   * @returns Promise resolving to migration path
   */
  generateMigrationPath(
    fromVersionId: string,
    toVersionId: string
  ): Promise<IMigrationPath>;

  /**
   * Execute version migration
   * @param migrationPath - Migration path to execute
   * @param options - Migration execution options
   * @returns Promise resolving to migration result
   */
  executeMigration(
    migrationPath: IMigrationPath,
    options?: IMigrationExecutionOptions
  ): Promise<IMigrationResult>;

  // ============================================================================
  // VERSION CONFLICT RESOLUTION
  // ============================================================================

  /**
   * Detect version conflicts
   * @param versionIds - Version identifiers to check
   * @returns Promise resolving to conflict detection result
   */
  detectConflicts(versionIds: string[]): Promise<IConflictDetectionResult>;

  /**
   * Resolve version conflicts
   * @param conflictId - Conflict identifier
   * @param resolutionStrategy - Resolution strategy to apply
   * @param options - Resolution options
   * @returns Promise resolving to conflict resolution result
   */
  resolveConflict(
    conflictId: string,
    resolutionStrategy: IResolutionStrategy,
    options?: IConflictResolutionOptions
  ): Promise<IConflictResolutionResult>;

  // ============================================================================
  // PERFORMANCE AND MONITORING
  // ============================================================================

  /**
   * Get version manager performance metrics
   * @returns Promise resolving to performance metrics
   */
  getPerformanceMetrics(): Promise<IVersionManagerMetrics>;

  /**
   * Optimize version storage
   * @param options - Optimization options
   * @returns Promise resolving to optimization result
   */
  optimizeStorage(options?: IStorageOptimizationOptions): Promise<IOptimizationResult>;
}

// ============================================================================
// SUPPORTING INTERFACES
// ============================================================================

/**
 * Version Creation Data Interface
 * Data structure for creating new versions
 */
export interface IVersionCreationData {
  /** Version number (semantic versioning) */
  version: string;
  /** Version description */
  description?: string;
  /** Rule content for this version */
  ruleContent: any;
  /** Version metadata */
  metadata?: Record<string, unknown>;
  /** Author information */
  author: string;
  /** Change log entries */
  changeLog?: IChangeLogEntry[];
}

/**
 * Version Creation Options Interface
 * Options for version creation process
 */
export interface IVersionCreationOptions {
  /** Skip backward compatibility validation */
  skipCompatibilityCheck?: boolean;
  /** Auto-increment version number */
  autoIncrement?: boolean;
  /** Create branch instead of direct version */
  createBranch?: boolean;
  /** Branch name if creating branch */
  branchName?: string;
}

/**
 * Version Update Data Interface
 * Data structure for updating existing versions
 */
export interface IVersionUpdateData {
  /** Updated description */
  description?: string;
  /** Updated rule content */
  ruleContent?: any;
  /** Updated metadata */
  metadata?: Record<string, unknown>;
  /** Additional change log entries */
  changeLog?: IChangeLogEntry[];
}

/**
 * Version Update Options Interface
 * Options for version update process
 */
export interface IVersionUpdateOptions {
  /** Validate compatibility after update */
  validateCompatibility?: boolean;
  /** Create backup before update */
  createBackup?: boolean;
  /** Force update even with conflicts */
  forceUpdate?: boolean;
}

/**
 * Version Deletion Options Interface
 * Options for version deletion process
 */
export interface IVersionDeletionOptions {
  /** Force deletion even with dependencies */
  forceDeletion?: boolean;
  /** Create backup before deletion */
  createBackup?: boolean;
  /** Cascade delete dependent versions */
  cascadeDelete?: boolean;
}

/**
 * Version Data Interface
 * Complete version information structure
 */
export interface IVersionData {
  /** Version identifier */
  versionId: string;
  /** Rule identifier */
  ruleId: string;
  /** Version number */
  version: string;
  /** Version description */
  description?: string;
  /** Rule content */
  ruleContent: any;
  /** Version status */
  status: 'draft' | 'active' | 'deprecated' | 'archived';
  /** Creation timestamp */
  createdAt: Date;
  /** Last update timestamp */
  updatedAt: Date;
  /** Author information */
  author: string;
  /** Version metadata */
  metadata: Record<string, unknown>;
  /** Change log */
  changeLog: IChangeLogEntry[];
  /** Parent version ID */
  parentVersionId?: string;
  /** Child version IDs */
  childVersionIds: string[];
  /** Branch information */
  branchInfo?: IBranchInfo;
  /** Tag information */
  tags: ITagInfo[];
}

/**
 * Version Filter Interface
 * Filtering options for version queries
 */
export interface IVersionFilter {
  /** Filter by status */
  status?: ('draft' | 'active' | 'deprecated' | 'archived')[];
  /** Filter by author */
  author?: string;
  /** Filter by date range */
  dateRange?: {
    from: Date;
    to: Date;
  };
  /** Filter by version pattern */
  versionPattern?: string;
  /** Include metadata in results */
  includeMetadata?: boolean;
  /** Maximum results to return */
  limit?: number;
  /** Results offset for pagination */
  offset?: number;
}

// ============================================================================
// CHANGE LOG AND METADATA INTERFACES
// ============================================================================

/**
 * Change Log Entry Interface
 * Individual change log entry structure
 */
export interface IChangeLogEntry {
  /** Entry identifier */
  entryId: string;
  /** Change timestamp */
  timestamp: Date;
  /** Change author */
  author: string;
  /** Change type */
  changeType: 'create' | 'update' | 'delete' | 'merge' | 'branch' | 'tag';
  /** Change description */
  description: string;
  /** Affected components */
  affectedComponents?: string[];
  /** Change impact level */
  impactLevel: 'low' | 'medium' | 'high' | 'critical';
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Branch Information Interface
 * Branch metadata and status information
 */
export interface IBranchInfo {
  /** Branch identifier */
  branchId: string;
  /** Branch name */
  branchName: string;
  /** Source version ID */
  sourceVersionId: string;
  /** Branch status */
  status: 'active' | 'merged' | 'abandoned';
  /** Creation timestamp */
  createdAt: Date;
  /** Branch author */
  author: string;
  /** Branch description */
  description?: string;
  /** Merge information if merged */
  mergeInfo?: IMergeInfo;
}

/**
 * Tag Information Interface
 * Version tag metadata
 */
export interface ITagInfo {
  /** Tag identifier */
  tagId: string;
  /** Tag name */
  tagName: string;
  /** Tag description */
  description?: string;
  /** Creation timestamp */
  createdAt: Date;
  /** Tag author */
  author: string;
  /** Tag type */
  tagType: 'release' | 'milestone' | 'hotfix' | 'feature' | 'custom';
  /** Tag metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Merge Information Interface
 * Details about branch merge operations
 */
export interface IMergeInfo {
  /** Merge identifier */
  mergeId: string;
  /** Target version ID */
  targetVersionId: string;
  /** Source version ID */
  sourceVersionId: string;
  /** Merge timestamp */
  mergedAt: Date;
  /** Merge author */
  mergedBy: string;
  /** Merge strategy used */
  strategy: 'fast-forward' | 'three-way' | 'squash' | 'rebase';
  /** Conflicts encountered */
  conflicts?: IConflictInfo[];
  /** Merge result status */
  status: 'success' | 'failed' | 'partial';
}

// ============================================================================
// FORWARD DECLARATIONS FOR REMAINING INTERFACES
// ============================================================================

// These interfaces will be defined in version-manager-types.ts to maintain
// separation of concerns and manage file size limits

export interface IBranchCreationData {
  branchName: string;
  description?: string;
  author: string;
}

export interface IMergeOptions {
  strategy?: 'fast-forward' | 'three-way' | 'squash' | 'rebase';
  conflictResolution?: 'auto' | 'manual';
  createBackup?: boolean;
}

export interface IMergeResult {
  success: boolean;
  mergeId: string;
  conflicts?: IConflictInfo[];
}

export interface ITagCreationData {
  tagName: string;
  tagType: 'release' | 'milestone' | 'hotfix' | 'feature' | 'custom';
  description?: string;
  author: string;
}

export interface ICompatibilityResult {
  compatible: boolean;
  issues: ICompatibilityIssue[];
  migrationRequired: boolean;
}

export interface IMigrationPath {
  pathId: string;
  steps: IMigrationStep[];
  estimatedDuration: number;
}

export interface IMigrationResult {
  success: boolean;
  executedSteps: number;
  errors: string[];
}

export interface IConflictDetectionResult {
  conflicts: IVersionConflict[];
  conflictCount: number;
}

export interface IConflictResolutionResult {
  resolved: boolean;
  strategy: string;
  outcome: string;
}

export interface IVersionManagerMetrics {
  totalVersions: number;
  activeVersions: number;
  performanceMetrics: Record<string, number>;
}

export interface IOptimizationResult {
  optimized: boolean;
  spaceSaved: number;
  duration: number;
}

// Basic conflict info interface
export interface IConflictInfo {
  conflictId: string;
  type: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Forward declarations for complex types (defined in types file)
export interface IMigrationExecutionOptions {}
export interface IResolutionStrategy {}
export interface IConflictResolutionOptions {}
export interface IStorageOptimizationOptions {}
export interface ICompatibilityIssue {}
export interface IMigrationStep {}
export interface IVersionConflict {}
