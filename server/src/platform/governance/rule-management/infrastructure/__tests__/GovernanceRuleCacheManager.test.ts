/**
 * @file GovernanceRuleCacheManager Test Suite
 * @filepath server/src/platform/governance/rule-management/infrastructure/__tests__/GovernanceRuleCacheManager.test.ts
 * @description Comprehensive test suite for GovernanceRuleCacheManager following OA Framework testing excellence standards
 * @coverage-target 95%+ across all metrics (statements, branches, functions, lines)
 * @testing-methodology Surgical precision testing with Anti-Simplification Policy compliance
 * @memory-safety MEM-SAFE-002 compliant (BaseTrackingService inheritance)
 * @created 2025-08-28
 */

// ============================================================================
// TABLE OF CONTENTS
// ============================================================================
// IMPORTS: Testing framework, target component, types, and utilities
// TEST SETUP: Configuration, mocks, and helper functions
// CONSTRUCTOR TESTS: Initialization and configuration validation
// CORE CACHE OPERATIONS: get, set, delete, has, clear methods
// CACHE MANAGEMENT: warming, invalidation, optimization
// STATISTICS & METRICS: cache statistics and performance metrics
// VALIDATION TESTS: doValidate method and health checks
// LIFECYCLE TESTS: initialization, shutdown, and cleanup
// ERROR HANDLING: exception scenarios and recovery
// EDGE CASES: boundary conditions and complex scenarios
// SURGICAL PRECISION: Hard-to-reach code paths and private methods
// ============================================================================

import { jest, describe, beforeEach, afterEach, it, expect } from '@jest/globals';

// Import target component
import { GovernanceRuleCacheManager } from '../GovernanceRuleCacheManager';

// Import types and interfaces
import {
  TCacheConfiguration,
  TCacheStatistics
} from '../../../../../../../shared/src/types/platform/governance/rule-management-types';

import {
  TValidationResult,
  TValidationError,
  TValidationWarning,
  TMetrics
} from '../../../../../../../shared/src/types/platform/tracking/tracking-types';

/**
 * Test configuration constants
 */
const TEST_CONFIG = {
  TIMEOUT_MS: 30000,
  MAX_CACHE_ENTRIES: 100,
  DEFAULT_TTL_MS: 60000,
  CLEANUP_INTERVAL_MS: 30000,
  TEST_CACHE_SIZE_MB: 10
};

/**
 * Create test cache configuration
 */
function createTestCacheConfiguration(overrides: Record<string, any> = {}): TCacheConfiguration {
  return {
    maxCacheSize: TEST_CONFIG.TEST_CACHE_SIZE_MB,
    defaultTtl: TEST_CONFIG.DEFAULT_TTL_MS,
    maxEntries: TEST_CONFIG.MAX_CACHE_ENTRIES,
    cleanupInterval: TEST_CONFIG.CLEANUP_INTERVAL_MS,
    evictionThreshold: 0.8,
    compressionEnabled: true,
    distributedCacheEnabled: false,
    ...overrides
  };
}

/**
 * Create test cache entry data
 */
function createTestCacheData(key: string = 'test-key', value: any = 'test-value'): Record<string, any> {
  return {
    key,
    value,
    metadata: {
      source: 'test',
      timestamp: new Date().toISOString()
    }
  };
}

/**
 * Create test rule result
 */
function createTestRuleResult(overrides: Record<string, any> = {}): Record<string, any> {
  return {
    ruleId: 'test-rule-001',
    result: 'passed',
    score: 85,
    timestamp: new Date(),
    metadata: {
      executionTime: 150,
      source: 'test'
    },
    ...overrides
  };
}

describe('GovernanceRuleCacheManager', () => {
  let cacheManager: GovernanceRuleCacheManager;

  beforeEach(async () => {
    // Create fresh instance for each test
    cacheManager = new GovernanceRuleCacheManager();
    await cacheManager.initialize();
  });

  afterEach(async () => {
    // Clean shutdown for each test
    if (cacheManager) {
      await cacheManager.shutdown();
    }
  });

  // ============================================================================
  // CONSTRUCTOR AND INITIALIZATION TESTS
  // ============================================================================

  describe('Constructor and Initialization', () => {
    it('should create GovernanceRuleCacheManager instance successfully', () => {
      const manager = new GovernanceRuleCacheManager();

      expect(manager).toBeInstanceOf(GovernanceRuleCacheManager);
      // Verify instance is created successfully
      expect(manager).toBeDefined();
      expect(typeof manager.initialize).toBe('function');
      expect(typeof manager.shutdown).toBe('function');
    });

    it('should initialize with default configuration', async () => {
      const manager = new GovernanceRuleCacheManager();
      await manager.initialize();

      // Verify initialization completed successfully
      expect(manager).toBeDefined();

      // Verify cache statistics are initialized
      const statistics = await manager.getCacheStatistics();
      expect(statistics).toBeDefined();
      expect(statistics.totalHits).toBe(0);

      await manager.shutdown();
    });

    it('should handle initialization errors gracefully', async () => {
      const manager = new GovernanceRuleCacheManager();
      
      // Mock initialization failure
      const originalInitialize = (manager as any).doInitialize;
      (manager as any).doInitialize = jest.fn().mockRejectedValue(new Error('Initialization failed'));

      await expect(manager.initialize()).rejects.toThrow('Initialization failed');

      // Restore original method
      (manager as any).doInitialize = originalInitialize;
    });

    it('should initialize cache statistics correctly', async () => {
      const statistics = await cacheManager.getCacheStatistics();

      expect(statistics).toHaveProperty('totalHits', 0);
      expect(statistics).toHaveProperty('totalMisses', 0);
      expect(statistics).toHaveProperty('totalEvictions', 0);
      expect(statistics).toHaveProperty('totalWarmups', 0);
      expect(statistics).toHaveProperty('hitRate', 0);
      expect(statistics).toHaveProperty('memoryUsage', 0);
      expect(statistics).toHaveProperty('entryCount', 0);
      expect(statistics).toHaveProperty('avgAccessTime', 0);
      expect(statistics).toHaveProperty('lastCleanup');
      expect(statistics).toHaveProperty('lastWarming');
    });
  });

  // ============================================================================
  // CORE CACHE OPERATIONS TESTS
  // ============================================================================

  describe('Core Cache Operations', () => {
    describe('set() method', () => {
      it('should store cache entry successfully', async () => {
        const key = 'test-key-001';
        const value = { data: 'test-value', timestamp: new Date() };

        await expect(cacheManager.set(key, value)).resolves.not.toThrow();

        // Verify entry was stored
        const retrieved = await cacheManager.get(key);
        expect(retrieved).toEqual(value);
      });

      it('should store cache entry with custom TTL', async () => {
        const key = 'test-key-ttl';
        const value = 'test-value-ttl';
        const customTtl = 30000; // 30 seconds

        await cacheManager.set(key, value, { ttl: customTtl });

        const retrieved = await cacheManager.get(key);
        expect(retrieved).toBe(value);
      });

      it('should store cache entry with dependencies', async () => {
        const key = 'test-key-deps';
        const value = 'test-value-deps';
        const dependencies = ['dep1', 'dep2'];

        await cacheManager.set(key, value, { dependencies });

        const retrieved = await cacheManager.get(key);
        expect(retrieved).toBe(value);
      });

      it('should store cache entry with tags', async () => {
        const key = 'test-key-tags';
        const value = 'test-value-tags';
        const tags = ['tag1', 'tag2'];

        await cacheManager.set(key, value, { tags });

        const retrieved = await cacheManager.get(key);
        expect(retrieved).toBe(value);
      });

      it('should handle cache capacity management', async () => {
        // Fill cache to trigger capacity management
        const promises = [];
        for (let i = 0; i < 50; i++) {
          promises.push(cacheManager.set(`key-${i}`, `value-${i}`));
        }

        await Promise.all(promises);

        // Verify cache is functioning
        const statistics = await cacheManager.getCacheStatistics();
        expect(statistics.entryCount).toBeGreaterThan(0);
      });

      it('should handle set operation errors', async () => {
        // Mock internal error
        const originalCreateCacheEntry = (cacheManager as any)._createCacheEntry;
        (cacheManager as any)._createCacheEntry = jest.fn().mockRejectedValue(new Error('Cache entry creation failed'));

        await expect(cacheManager.set('error-key', 'error-value')).rejects.toThrow('Cache entry creation failed');

        // Restore original method
        (cacheManager as any)._createCacheEntry = originalCreateCacheEntry;
      });
    });

    describe('get() method', () => {
      it('should retrieve existing cache entry', async () => {
        const key = 'get-test-key';
        const value = { data: 'get-test-value', id: 123 };

        await cacheManager.set(key, value);
        const retrieved = await cacheManager.get(key);

        expect(retrieved).toEqual(value);
      });

      it('should return null for non-existent key', async () => {
        const retrieved = await cacheManager.get('non-existent-key');
        expect(retrieved).toBeNull();
      });

      it('should update access statistics on cache hit', async () => {
        const key = 'stats-test-key';
        const value = 'stats-test-value';

        await cacheManager.set(key, value);
        
        // Get initial statistics
        const initialStats = await cacheManager.getCacheStatistics();
        const initialHits = initialStats.totalHits;

        // Access the cached value
        await cacheManager.get(key);

        // Verify hit count increased
        const updatedStats = await cacheManager.getCacheStatistics();
        expect(updatedStats.totalHits).toBe(initialHits + 1);
      });

      it('should update miss statistics on cache miss', async () => {
        // Get initial statistics
        const initialStats = await cacheManager.getCacheStatistics();
        const initialMisses = initialStats.totalMisses;

        // Access non-existent key
        await cacheManager.get('non-existent-key');

        // Verify miss count increased
        const updatedStats = await cacheManager.getCacheStatistics();
        expect(updatedStats.totalMisses).toBe(initialMisses + 1);
      });

      it('should handle get operation errors', async () => {
        // Mock internal error in cache retrieval
        const originalL1Cache = (cacheManager as any)._l1Cache;
        (cacheManager as any)._l1Cache = {
          get: jest.fn().mockImplementation(() => {
            throw new Error('Cache access failed');
          })
        };

        await expect(cacheManager.get('error-key')).rejects.toThrow('Cache access failed');

        // Restore original cache
        (cacheManager as any)._l1Cache = originalL1Cache;
      });
    });

    describe('delete() method', () => {
      it('should delete existing cache entry', async () => {
        const key = 'delete-test-key';
        const value = 'delete-test-value';

        await cacheManager.set(key, value);
        
        // Verify entry exists
        expect(await cacheManager.has(key)).toBe(true);

        // Delete entry
        const deleted = await cacheManager.delete(key);
        expect(deleted).toBe(true);

        // Verify entry no longer exists
        expect(await cacheManager.has(key)).toBe(false);
      });

      it('should return false for non-existent key deletion', async () => {
        const deleted = await cacheManager.delete('non-existent-key');
        expect(deleted).toBe(false);
      });

      it('should update statistics after deletion', async () => {
        const key = 'delete-stats-key';
        const value = 'delete-stats-value';

        await cacheManager.set(key, value);
        
        const initialStats = await cacheManager.getCacheStatistics();
        const initialCount = initialStats.entryCount;

        await cacheManager.delete(key);

        const updatedStats = await cacheManager.getCacheStatistics();
        expect(updatedStats.entryCount).toBe(initialCount - 1);
      });

      it('should handle delete operation errors', async () => {
        // Mock internal error by corrupting the cache map
        const originalL1Cache = (cacheManager as any)._l1Cache;
        (cacheManager as any)._l1Cache = {
          delete: jest.fn().mockImplementation(() => {
            throw new Error('Cache deletion failed');
          })
        };

        await expect(cacheManager.delete('error-key')).rejects.toThrow('Cache deletion failed');

        // Restore original cache
        (cacheManager as any)._l1Cache = originalL1Cache;
      });
    });

    describe('has() method', () => {
      it('should return true for existing valid entry', async () => {
        const key = 'has-test-key';
        const value = 'has-test-value';

        await cacheManager.set(key, value);
        
        const exists = await cacheManager.has(key);
        expect(exists).toBe(true);
      });

      it('should return false for non-existent entry', async () => {
        const exists = await cacheManager.has('non-existent-key');
        expect(exists).toBe(false);
      });

      it('should return false for expired entry', async () => {
        // Mock expired entry directly in cache
        const key = 'expired-test-key';
        const expiredEntry = {
          value: 'expired-value',
          createdAt: new Date(Date.now() - 120000), // 2 minutes ago
          ttl: 60000, // 1 minute TTL (expired)
          lastAccessed: new Date(Date.now() - 120000)
        };

        const l1Cache = (cacheManager as any)._l1Cache;
        l1Cache.set(key, expiredEntry);

        const exists = await cacheManager.has(key);
        expect(exists).toBe(false);
      });

      it('should handle has operation errors', async () => {
        // Mock internal error
        const originalL1Cache = (cacheManager as any)._l1Cache;
        (cacheManager as any)._l1Cache = {
          get: jest.fn().mockImplementation(() => {
            throw new Error('Cache access failed');
          })
        };

        await expect(cacheManager.has('error-key')).rejects.toThrow('Cache access failed');

        // Restore original cache
        (cacheManager as any)._l1Cache = originalL1Cache;
      });
    });

    describe('clear() method', () => {
      it('should clear all cache entries', async () => {
        // Add multiple entries
        await cacheManager.set('key1', 'value1');
        await cacheManager.set('key2', 'value2');
        await cacheManager.set('key3', 'value3');

        const clearedCount = await cacheManager.clear();
        expect(clearedCount).toBe(3);

        // Verify all entries are cleared
        expect(await cacheManager.has('key1')).toBe(false);
        expect(await cacheManager.has('key2')).toBe(false);
        expect(await cacheManager.has('key3')).toBe(false);

        const statistics = await cacheManager.getCacheStatistics();
        expect(statistics.entryCount).toBe(0);
      });

      it('should clear cache entries by pattern', async () => {
        // Add entries with different patterns
        await cacheManager.set('user:123', 'user-data-123');
        await cacheManager.set('user:456', 'user-data-456');
        await cacheManager.set('session:abc', 'session-data-abc');

        const clearedCount = await cacheManager.clear('user:.*');
        expect(clearedCount).toBe(2);

        // Verify only user entries are cleared
        expect(await cacheManager.has('user:123')).toBe(false);
        expect(await cacheManager.has('user:456')).toBe(false);
        expect(await cacheManager.has('session:abc')).toBe(true);
      });

      it('should return 0 when clearing empty cache', async () => {
        const clearedCount = await cacheManager.clear();
        expect(clearedCount).toBe(0);
      });

      it('should handle clear operation errors', async () => {
        // Mock internal error
        const originalL1Cache = (cacheManager as any)._l1Cache;
        (cacheManager as any)._l1Cache = {
          size: 1,
          entries: jest.fn().mockImplementation(() => {
            throw new Error('Cache iteration failed');
          })
        };

        await expect(cacheManager.clear('test-pattern')).rejects.toThrow('Cache iteration failed');

        // Restore original cache
        (cacheManager as any)._l1Cache = originalL1Cache;
      });
    });
  });

  // ============================================================================
  // CACHE MANAGEMENT TESTS
  // ============================================================================

  describe('Cache Management Operations', () => {
    describe('warmCache() method', () => {
      it('should warm cache with all strategies', async () => {
        const warmedCount = await cacheManager.warmCache();

        expect(typeof warmedCount).toBe('number');
        expect(warmedCount).toBeGreaterThanOrEqual(0);

        const statistics = await cacheManager.getCacheStatistics();
        expect(statistics.totalWarmups).toBeGreaterThanOrEqual(warmedCount);
      });

      it('should warm cache with specific strategy', async () => {
        const strategyId = 'test-strategy-001';
        const warmedCount = await cacheManager.warmCache(strategyId);

        expect(typeof warmedCount).toBe('number');
        expect(warmedCount).toBeGreaterThanOrEqual(0);
      });

      it('should handle warming strategy not found', async () => {
        const warmedCount = await cacheManager.warmCache('non-existent-strategy');
        expect(warmedCount).toBe(0);
      });

      it('should handle warming errors gracefully', async () => {
        // Mock warming strategies to force error
        const originalWarmingStrategies = (cacheManager as any)._warmingStrategies;
        (cacheManager as any)._warmingStrategies = new Map([
          ['error-strategy', {
            strategyId: 'error-strategy',
            enabled: true,
            rules: [{ pattern: 'test-*', priority: 1 }]
          }]
        ]);

        // Mock _executeWarmingStrategy to throw error
        const originalExecuteWarmingStrategy = (cacheManager as any)._executeWarmingStrategy;
        (cacheManager as any)._executeWarmingStrategy = jest.fn().mockRejectedValue(new Error('Warming failed'));

        await expect(cacheManager.warmCache()).rejects.toThrow('Warming failed');

        // Restore original methods
        (cacheManager as any)._executeWarmingStrategy = originalExecuteWarmingStrategy;
        (cacheManager as any)._warmingStrategies = originalWarmingStrategies;
      });
    });

    describe('invalidateByDependency() method', () => {
      it('should invalidate entries by dependency', async () => {
        const dependency = 'test-dependency';

        // Add entries with and without the dependency
        await cacheManager.set('key1', 'value1', { dependencies: [dependency, 'other-dep'] });
        await cacheManager.set('key2', 'value2', { dependencies: ['other-dep'] });
        await cacheManager.set('key3', 'value3', { dependencies: [dependency] });

        const invalidatedCount = await cacheManager.invalidateByDependency(dependency);
        expect(invalidatedCount).toBe(2);

        // Verify correct entries were invalidated
        expect(await cacheManager.has('key1')).toBe(false);
        expect(await cacheManager.has('key2')).toBe(true);
        expect(await cacheManager.has('key3')).toBe(false);
      });

      it('should return 0 when no entries match dependency', async () => {
        await cacheManager.set('key1', 'value1', { dependencies: ['other-dep'] });

        const invalidatedCount = await cacheManager.invalidateByDependency('non-existent-dep');
        expect(invalidatedCount).toBe(0);
      });

      it('should handle invalidation errors', async () => {
        // Mock internal error
        const originalL1Cache = (cacheManager as any)._l1Cache;
        (cacheManager as any)._l1Cache = {
          entries: jest.fn().mockImplementation(() => {
            throw new Error('Cache iteration failed');
          })
        };

        await expect(cacheManager.invalidateByDependency('test-dep')).rejects.toThrow('Cache iteration failed');

        // Restore original cache
        (cacheManager as any)._l1Cache = originalL1Cache;
      });
    });

    describe('optimizeCache() method', () => {
      it('should optimize cache performance', async () => {
        // Add some entries to optimize
        for (let i = 0; i < 10; i++) {
          await cacheManager.set(`optimize-key-${i}`, `optimize-value-${i}`);
        }

        await expect(cacheManager.optimizeCache()).resolves.not.toThrow();

        // Verify cache is still functional after optimization
        const statistics = await cacheManager.getCacheStatistics();
        expect(statistics.entryCount).toBeGreaterThanOrEqual(0);
      });

      it('should handle optimization errors', async () => {
        // Mock optimization error
        const originalPerformCacheOptimization = (cacheManager as any)._performCacheOptimization;
        (cacheManager as any)._performCacheOptimization = jest.fn().mockRejectedValue(new Error('Optimization failed'));

        await expect(cacheManager.optimizeCache()).rejects.toThrow('Optimization failed');

        // Restore original method
        (cacheManager as any)._performCacheOptimization = originalPerformCacheOptimization;
      });
    });

    describe('configureCaching() method', () => {
      it('should configure cache settings successfully', async () => {
        const configuration = createTestCacheConfiguration({
          maxCacheSize: 128,
          defaultTtl: 120000,
          maxEntries: 500
        });

        await expect(cacheManager.configureCaching(configuration)).resolves.not.toThrow();
      });

      it('should handle configuration errors', async () => {
        // Mock configuration error
        const originalUpdateCacheConfiguration = (cacheManager as any)._updateCacheConfiguration;
        (cacheManager as any)._updateCacheConfiguration = jest.fn().mockRejectedValue(new Error('Configuration failed'));

        const configuration = createTestCacheConfiguration();
        await expect(cacheManager.configureCaching(configuration)).rejects.toThrow('Configuration failed');

        // Restore original method
        (cacheManager as any)._updateCacheConfiguration = originalUpdateCacheConfiguration;
      });
    });
  });

  // ============================================================================
  // RULE-SPECIFIC CACHE OPERATIONS TESTS
  // ============================================================================

  describe('Rule-Specific Cache Operations', () => {
    describe('cacheRuleResult() method', () => {
      it('should cache rule result successfully', async () => {
        const key = 'rule-result-001';
        const result = createTestRuleResult();

        await expect(cacheManager.cacheRuleResult(key, result)).resolves.not.toThrow();

        // Verify result was cached
        const cached = await cacheManager.getCachedResult(key);
        expect(cached).toEqual(result);
      });

      it('should cache rule result with custom TTL', async () => {
        const key = 'rule-result-ttl';
        const result = createTestRuleResult();
        const customTtl = 30000;

        await cacheManager.cacheRuleResult(key, result, customTtl);

        const cached = await cacheManager.getCachedResult(key);
        expect(cached).toEqual(result);
      });

      it('should handle caching errors', async () => {
        // Mock set method error
        const originalSet = cacheManager.set;
        cacheManager.set = jest.fn().mockRejectedValue(new Error('Set operation failed'));

        const key = 'error-rule-result';
        const result = createTestRuleResult();

        await expect(cacheManager.cacheRuleResult(key, result)).rejects.toThrow('Set operation failed');

        // Restore original method
        cacheManager.set = originalSet;
      });
    });

    describe('getCachedResult() method', () => {
      it('should retrieve cached rule result', async () => {
        const key = 'cached-rule-001';
        const result = createTestRuleResult();

        await cacheManager.cacheRuleResult(key, result);
        const retrieved = await cacheManager.getCachedResult(key);

        expect(retrieved).toEqual(result);
      });

      it('should return null for non-existent cached result', async () => {
        const retrieved = await cacheManager.getCachedResult('non-existent-rule');
        expect(retrieved).toBeNull();
      });

      it('should handle retrieval errors', async () => {
        // Mock get method error
        const originalGet = cacheManager.get;
        cacheManager.get = jest.fn().mockRejectedValue(new Error('Get operation failed'));

        await expect(cacheManager.getCachedResult('error-key')).rejects.toThrow('Get operation failed');

        // Restore original method
        cacheManager.get = originalGet;
      });
    });

    describe('invalidateCache() method', () => {
      it('should invalidate specific cache entry', async () => {
        const key = 'invalidate-test';
        const result = createTestRuleResult();

        await cacheManager.cacheRuleResult(key, result);
        expect(await cacheManager.has(key)).toBe(true);

        await cacheManager.invalidateCache(key);
        expect(await cacheManager.has(key)).toBe(false);
      });

      it('should handle invalidation of non-existent entry', async () => {
        await expect(cacheManager.invalidateCache('non-existent-key')).resolves.not.toThrow();
      });

      it('should handle invalidation errors', async () => {
        // Mock delete method error
        const originalDelete = cacheManager.delete;
        cacheManager.delete = jest.fn().mockRejectedValue(new Error('Delete operation failed'));

        await expect(cacheManager.invalidateCache('error-key')).rejects.toThrow('Delete operation failed');

        // Restore original method
        cacheManager.delete = originalDelete;
      });
    });

    describe('clearAllCache() method', () => {
      it('should clear all cached entries', async () => {
        // Add multiple rule results
        await cacheManager.cacheRuleResult('rule1', createTestRuleResult({ ruleId: 'rule1' }));
        await cacheManager.cacheRuleResult('rule2', createTestRuleResult({ ruleId: 'rule2' }));
        await cacheManager.cacheRuleResult('rule3', createTestRuleResult({ ruleId: 'rule3' }));

        await cacheManager.clearAllCache();

        // Verify all entries are cleared
        expect(await cacheManager.has('rule1')).toBe(false);
        expect(await cacheManager.has('rule2')).toBe(false);
        expect(await cacheManager.has('rule3')).toBe(false);

        const statistics = await cacheManager.getCacheStatistics();
        expect(statistics.entryCount).toBe(0);
      });

      it('should handle clear all errors', async () => {
        // Mock clear method error
        const originalClear = cacheManager.clear;
        cacheManager.clear = jest.fn().mockRejectedValue(new Error('Clear operation failed'));

        await expect(cacheManager.clearAllCache()).rejects.toThrow('Clear operation failed');

        // Restore original method
        cacheManager.clear = originalClear;
      });
    });
  });

  // ============================================================================
  // STATISTICS AND METRICS TESTS
  // ============================================================================

  describe('Statistics and Metrics', () => {
    describe('getCacheStatistics() method', () => {
      it('should return comprehensive cache statistics', async () => {
        // Perform some cache operations to generate statistics
        await cacheManager.set('stats-key-1', 'stats-value-1');
        await cacheManager.set('stats-key-2', 'stats-value-2');
        await cacheManager.get('stats-key-1'); // Hit
        await cacheManager.get('non-existent-key'); // Miss

        const statistics = await cacheManager.getCacheStatistics();

        expect(statistics).toHaveProperty('totalHits');
        expect(statistics).toHaveProperty('totalMisses');
        expect(statistics).toHaveProperty('totalEvictions');
        expect(statistics).toHaveProperty('totalWarmups');
        expect(statistics).toHaveProperty('hitRate');
        expect(statistics).toHaveProperty('memoryUsage');
        expect(statistics).toHaveProperty('entryCount');
        expect(statistics).toHaveProperty('avgAccessTime');
        expect(statistics).toHaveProperty('lastCleanup');
        expect(statistics).toHaveProperty('lastWarming');

        expect(statistics.totalHits).toBeGreaterThan(0);
        expect(statistics.totalMisses).toBeGreaterThan(0);
        expect(statistics.entryCount).toBe(2);
      });

      it('should handle statistics calculation errors', async () => {
        // Mock statistics calculation error
        const originalUpdateMemoryUsage = (cacheManager as any)._updateMemoryUsage;
        (cacheManager as any)._updateMemoryUsage = jest.fn().mockImplementation(() => {
          throw new Error('Memory usage calculation failed');
        });

        await expect(cacheManager.getCacheStatistics()).rejects.toThrow('Memory usage calculation failed');

        // Restore original method
        (cacheManager as any)._updateMemoryUsage = originalUpdateMemoryUsage;
      });
    });

    describe('getMetrics() method', () => {
      it('should return comprehensive service metrics', async () => {
        const metrics = await cacheManager.getMetrics();

        expect(metrics).toHaveProperty('custom');
        expect(metrics.custom).toHaveProperty('totalHits');
        expect(metrics.custom).toHaveProperty('totalMisses');
        expect(metrics.custom).toHaveProperty('totalEvictions');
        expect(metrics.custom).toHaveProperty('totalWarmups');
        expect(metrics.custom).toHaveProperty('hitRate');
        expect(metrics.custom).toHaveProperty('memoryUsage');
        expect(metrics.custom).toHaveProperty('entryCount');
        expect(metrics.custom).toHaveProperty('avgAccessTime');
        expect(metrics.custom).toHaveProperty('cacheTiers');
        expect(metrics.custom).toHaveProperty('warmingStrategies');
        expect(metrics.custom).toHaveProperty('invalidationRules');
      });

      it('should handle metrics collection errors', async () => {
        // Mock base metrics error
        const originalGetMetrics = Object.getPrototypeOf(Object.getPrototypeOf(cacheManager)).getMetrics;
        Object.getPrototypeOf(Object.getPrototypeOf(cacheManager)).getMetrics = jest.fn().mockRejectedValue(new Error('Base metrics failed'));

        await expect(cacheManager.getMetrics()).rejects.toThrow('Base metrics failed');

        // Restore original method
        Object.getPrototypeOf(Object.getPrototypeOf(cacheManager)).getMetrics = originalGetMetrics;
      });
    });
  });

  // ============================================================================
  // VALIDATION TESTS
  // ============================================================================

  describe('Validation Operations', () => {
    describe('doValidate() method', () => {
      it('should perform comprehensive cache manager validation', async () => {
        const result = await (cacheManager as any).doValidate();

        expect(result).toHaveProperty('validationId');
        expect(result).toHaveProperty('componentId', 'governance-rule-cache-manager');
        expect(result).toHaveProperty('timestamp');
        expect(result).toHaveProperty('status');
        expect(result).toHaveProperty('overallScore');
        expect(result).toHaveProperty('checks');
        expect(result).toHaveProperty('references');
        expect(result).toHaveProperty('recommendations');
        expect(result).toHaveProperty('warnings');
        expect(result).toHaveProperty('errors');
        expect(result).toHaveProperty('metadata');

        expect(['valid', 'invalid']).toContain(result.status);
        expect(result.overallScore).toBeGreaterThanOrEqual(0);
        expect(result.overallScore).toBeLessThanOrEqual(100);
      });

      it('should detect low cache hit rate warnings', async () => {
        // Generate low hit rate scenario
        const cacheStats = (cacheManager as any)._cacheStatistics;
        cacheStats.totalHits = 10;
        cacheStats.totalMisses = 200; // Low hit rate: ~4.8%

        const result = await (cacheManager as any).doValidate();

        expect(result.warnings.length).toBeGreaterThan(0);
        expect(result.warnings.some((w: string) => w.includes('Low cache hit rate'))).toBe(true);
      });

      it('should detect missing cache tiers errors', async () => {
        // Clear cache tiers to trigger error
        const originalCacheTiers = (cacheManager as any)._cacheTiers;
        (cacheManager as any)._cacheTiers = new Map();

        const result = await (cacheManager as any).doValidate();

        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors.some((e: string) => e.includes('No cache tiers configured'))).toBe(true);
        expect(result.status).toBe('invalid');

        // Restore original cache tiers
        (cacheManager as any)._cacheTiers = originalCacheTiers;
      });

      it('should detect high memory usage warnings', async () => {
        // Simulate high memory usage
        const cacheStats = (cacheManager as any)._cacheStatistics;
        const maxMemory = (cacheManager as any)._cacheConfig.MAX_CACHE_SIZE_MB * 1024 * 1024;
        cacheStats.memoryUsage = maxMemory * 0.95; // 95% usage

        const result = await (cacheManager as any).doValidate();

        expect(result.warnings.length).toBeGreaterThan(0);
        expect(result.warnings.some((w: string) => w.includes('High memory usage'))).toBe(true);
      });

      it('should handle validation errors gracefully', async () => {
        // Mock validation error
        const originalValidateCacheManagerHealth = (cacheManager as any)._validateCacheManagerHealth;
        (cacheManager as any)._validateCacheManagerHealth = jest.fn().mockRejectedValue(new Error('Validation failed'));

        await expect((cacheManager as any).doValidate()).rejects.toThrow('Validation failed');

        // Restore original method
        (cacheManager as any)._validateCacheManagerHealth = originalValidateCacheManagerHealth;
      });
    });
  });

  // ============================================================================
  // LIFECYCLE TESTS
  // ============================================================================

  describe('Lifecycle Management', () => {
    describe('Initialization', () => {
      it('should initialize cache statistics correctly', async () => {
        const manager = new GovernanceRuleCacheManager();
        await manager.initialize();

        const statistics = await manager.getCacheStatistics();
        expect(statistics.totalHits).toBe(0);
        expect(statistics.totalMisses).toBe(0);
        expect(statistics.entryCount).toBe(0);

        await manager.shutdown();
      });

      it('should initialize cache tiers correctly', async () => {
        const manager = new GovernanceRuleCacheManager();
        await manager.initialize();

        // Verify cache tiers are initialized
        const cacheTiers = (manager as any)._cacheTiers;
        expect(cacheTiers.size).toBeGreaterThan(0);
        expect(cacheTiers.has('l1-memory')).toBe(true);

        await manager.shutdown();
      });

      it('should start cleanup interval correctly', async () => {
        const manager = new GovernanceRuleCacheManager();

        await manager.initialize();

        // Verify manager is initialized and functional
        expect(manager).toBeDefined();

        // Verify cache operations work (indicating proper initialization)
        await manager.set('test-key', 'test-value');
        expect(await manager.has('test-key')).toBe(true);

        await manager.shutdown();
      });

      it('should handle initialization component failures', async () => {
        const manager = new GovernanceRuleCacheManager();

        // Mock initialization failure
        const originalInitializeCacheStatistics = (manager as any)._initializeCacheStatistics;
        (manager as any)._initializeCacheStatistics = jest.fn().mockRejectedValue(new Error('Cache statistics initialization failed'));

        await expect(manager.initialize()).rejects.toThrow('Cache statistics initialization failed');

        // Restore original method
        (manager as any)._initializeCacheStatistics = originalInitializeCacheStatistics;
      });
    });

    describe('Shutdown', () => {
      it('should shutdown gracefully', async () => {
        const manager = new GovernanceRuleCacheManager();
        await manager.initialize();

        // Add some data
        await manager.set('shutdown-test', 'shutdown-value');

        await expect(manager.shutdown()).resolves.not.toThrow();

        // Verify shutdown completed successfully
        expect(manager).toBeDefined();
      });

      it('should clear warming interval on shutdown', async () => {
        const manager = new GovernanceRuleCacheManager();
        await manager.initialize();

        // Set warming interval
        (manager as any)._warmingIntervalId = setInterval(() => {}, 1000);

        const clearIntervalSpy = jest.spyOn(global, 'clearInterval');

        await manager.shutdown();

        expect(clearIntervalSpy).toHaveBeenCalled();

        clearIntervalSpy.mockRestore();
      });

      it('should persist cache state on shutdown', async () => {
        const manager = new GovernanceRuleCacheManager();
        await manager.initialize();

        // Mock persist cache state
        const persistCacheStateSpy = jest.spyOn(manager as any, '_persistCacheState').mockResolvedValue(undefined);

        await manager.shutdown();

        expect(persistCacheStateSpy).toHaveBeenCalled();

        persistCacheStateSpy.mockRestore();
      });

      it('should handle shutdown errors gracefully', async () => {
        const manager = new GovernanceRuleCacheManager();
        await manager.initialize();

        // Mock shutdown error
        const originalPersistCacheState = (manager as any)._persistCacheState;
        (manager as any)._persistCacheState = jest.fn().mockRejectedValue(new Error('Persist failed'));

        await expect(manager.shutdown()).rejects.toThrow('Persist failed');

        // Restore original method
        (manager as any)._persistCacheState = originalPersistCacheState;
      });
    });

    describe('doTrack() method', () => {
      it('should track cache manager data successfully', async () => {
        const trackingData = {
          operation: 'cache-operation',
          key: 'test-key',
          result: 'success',
          timestamp: new Date()
        };

        await expect((cacheManager as any).doTrack(trackingData)).resolves.not.toThrow();
      });

      it('should handle tracking errors gracefully', async () => {
        // Mock logging error
        const originalLogOperation = cacheManager.logOperation;
        cacheManager.logOperation = jest.fn().mockImplementation(() => {
          throw new Error('Logging failed');
        });

        const trackingData = { test: 'data' };

        await expect((cacheManager as any).doTrack(trackingData)).rejects.toThrow('Logging failed');

        // Restore original method
        cacheManager.logOperation = originalLogOperation;
      });
    });
  });

  // ============================================================================
  // EDGE CASES AND BOUNDARY CONDITIONS
  // ============================================================================

  describe('Edge Cases and Boundary Conditions', () => {
    describe('Cache Entry Expiration', () => {
      it('should handle expired entries correctly', async () => {
        // Mock expired entry directly in cache
        const key = 'expiring-key';
        const expiredEntry = {
          value: 'expiring-value',
          createdAt: new Date(Date.now() - 120000), // 2 minutes ago
          ttl: 60000, // 1 minute TTL (expired)
          lastAccessed: new Date(Date.now() - 120000)
        };

        const l1Cache = (cacheManager as any)._l1Cache;
        l1Cache.set(key, expiredEntry);

        // Verify entry is expired
        expect(await cacheManager.has(key)).toBe(false);
        expect(await cacheManager.get(key)).toBeNull();
      });

      it('should clean up expired entries during periodic cleanup', async () => {
        // Mock expired entries directly in cache
        const l1Cache = (cacheManager as any)._l1Cache;

        // Add expired entries
        l1Cache.set('expire1', {
          value: 'value1',
          createdAt: new Date(Date.now() - 120000), // 2 minutes ago
          ttl: 60000, // 1 minute TTL (expired)
          lastAccessed: new Date(Date.now() - 120000)
        });

        l1Cache.set('expire2', {
          value: 'value2',
          createdAt: new Date(Date.now() - 120000), // 2 minutes ago
          ttl: 60000, // 1 minute TTL (expired)
          lastAccessed: new Date(Date.now() - 120000)
        });

        // Add valid entry
        await cacheManager.set('persist', 'value-persist', { ttl: 60000 });

        // Trigger periodic cleanup
        await (cacheManager as any)._performRuleCacheManagerPeriodicCleanup();

        // Verify expired entries are cleaned up
        expect(await cacheManager.has('expire1')).toBe(false);
        expect(await cacheManager.has('expire2')).toBe(false);
        expect(await cacheManager.has('persist')).toBe(true);
      });
    });

    describe('Cache Capacity Management', () => {
      it('should handle cache eviction when capacity is reached', async () => {
        // Mock smaller cache size for testing
        const originalConfig = (cacheManager as any)._cacheConfig;
        (cacheManager as any)._cacheConfig = {
          ...originalConfig,
          MAX_CACHE_ENTRIES: 5,
          EVICTION_THRESHOLD: 0.8
        };

        // Fill cache beyond threshold
        for (let i = 0; i < 10; i++) {
          await cacheManager.set(`capacity-key-${i}`, `capacity-value-${i}`);
        }

        // Manually trigger eviction
        await (cacheManager as any)._ensureCacheCapacity();

        // Verify eviction occurred
        const statistics = await cacheManager.getCacheStatistics();
        expect(statistics.entryCount).toBeGreaterThan(0); // Some entries should remain
        expect(statistics.entryCount).toBeLessThan(10); // But not all 10

        // Restore original config
        (cacheManager as any)._cacheConfig = originalConfig;
      });

      it('should use LRU eviction strategy correctly', async () => {
        // Mock smaller cache for testing
        const originalConfig = (cacheManager as any)._cacheConfig;
        (cacheManager as any)._cacheConfig = {
          ...originalConfig,
          MAX_CACHE_ENTRIES: 3,
          EVICTION_THRESHOLD: 0.8
        };

        // Add entries
        await cacheManager.set('lru1', 'value1');
        await cacheManager.set('lru2', 'value2');
        await cacheManager.set('lru3', 'value3');

        // Access lru1 to make it recently used
        await cacheManager.get('lru1');

        // Add new entry to trigger eviction
        await cacheManager.set('lru4', 'value4');

        // lru2 should be evicted (least recently used)
        expect(await cacheManager.has('lru1')).toBe(true); // Recently accessed
        expect(await cacheManager.has('lru3')).toBe(true); // Recently created
        expect(await cacheManager.has('lru4')).toBe(true); // Just added

        // Restore original config
        (cacheManager as any)._cacheConfig = originalConfig;
      });
    });

    describe('Error Handling Edge Cases', () => {
      it('should handle JSON serialization failures gracefully', async () => {
        // Create circular reference object
        const circularObj: any = { name: 'test' };
        circularObj.self = circularObj;

        // Mock _calculateEntrySize to handle circular reference
        const originalCalculateEntrySize = (cacheManager as any)._calculateEntrySize;
        (cacheManager as any)._calculateEntrySize = jest.fn().mockImplementation((value) => {
          try {
            return JSON.stringify(value).length * 2;
          } catch {
            return 1024; // Default size for serialization failures
          }
        });

        await expect(cacheManager.set('circular-key', circularObj)).resolves.not.toThrow();

        // Restore original method
        (cacheManager as any)._calculateEntrySize = originalCalculateEntrySize;
      });

      it('should handle invalid tier specifications', async () => {
        const key = 'invalid-tier-key';
        const value = 'invalid-tier-value';

        // Test with invalid tier - should default to L1_MEMORY
        await cacheManager.set(key, value, { tier: 'invalid-tier' as any });

        const retrieved = await cacheManager.get(key);
        expect(retrieved).toBe(value);
      });

      it('should handle empty and null values correctly', async () => {
        // Test null value
        await cacheManager.set('null-key', null);
        expect(await cacheManager.get('null-key')).toBeNull();

        // Test undefined value
        await cacheManager.set('undefined-key', undefined);
        expect(await cacheManager.get('undefined-key')).toBeUndefined();

        // Test empty string
        await cacheManager.set('empty-key', '');
        expect(await cacheManager.get('empty-key')).toBe('');

        // Test empty object
        await cacheManager.set('empty-obj-key', {});
        expect(await cacheManager.get('empty-obj-key')).toEqual({});
      });
    });

    describe('Concurrent Operations', () => {
      it('should handle concurrent cache operations safely', async () => {
        const promises = [];

        // Perform concurrent operations
        for (let i = 0; i < 20; i++) {
          promises.push(cacheManager.set(`concurrent-${i}`, `value-${i}`));
        }

        await Promise.all(promises);

        // Verify all entries were stored
        for (let i = 0; i < 20; i++) {
          expect(await cacheManager.has(`concurrent-${i}`)).toBe(true);
        }
      });

      it('should handle concurrent get/set operations', async () => {
        const key = 'concurrent-key';
        const promises = [];

        // Mix of get and set operations
        for (let i = 0; i < 10; i++) {
          promises.push(cacheManager.set(key, `value-${i}`));
          promises.push(cacheManager.get(key));
        }

        await Promise.all(promises);

        // Verify final state is consistent
        const finalValue = await cacheManager.get(key);
        expect(typeof finalValue).toBe('string');
        expect(finalValue).toMatch(/^value-\d+$/);
      });
    });
  });

  // ============================================================================
  // SURGICAL PRECISION TESTING - HARD-TO-REACH CODE PATHS
  // ============================================================================

  describe('Surgical Precision Testing', () => {
    describe('Private Method Testing', () => {
      it('should test _isEntryValid method directly', () => {
        const isEntryValidMethod = (cacheManager as any)._isEntryValid.bind(cacheManager);

        // Test valid entry
        const validEntry = {
          createdAt: new Date(),
          ttl: 60000 // 1 minute
        };
        expect(isEntryValidMethod(validEntry)).toBe(true);

        // Test expired entry
        const expiredEntry = {
          createdAt: new Date(Date.now() - 120000), // 2 minutes ago
          ttl: 60000 // 1 minute TTL
        };
        expect(isEntryValidMethod(expiredEntry)).toBe(false);
      });

      it('should test _calculateEntrySize method directly', () => {
        const calculateEntrySizeMethod = (cacheManager as any)._calculateEntrySize.bind(cacheManager);

        // Test string value
        const stringSize = calculateEntrySizeMethod('test string');
        expect(stringSize).toBeGreaterThan(0);

        // Test object value
        const objectSize = calculateEntrySizeMethod({ key: 'value', number: 123 });
        expect(objectSize).toBeGreaterThan(stringSize);

        // Test circular reference (should return default size)
        const circular: any = {};
        circular.self = circular;
        const circularSize = calculateEntrySizeMethod(circular);
        expect(circularSize).toBe(1024); // Default size
      });

      it('should test _updateHitRate method directly', () => {
        const updateHitRateMethod = (cacheManager as any)._updateHitRate.bind(cacheManager);
        const cacheStats = (cacheManager as any)._cacheStatistics;

        // Test with no hits or misses
        cacheStats.totalHits = 0;
        cacheStats.totalMisses = 0;
        updateHitRateMethod();
        expect(cacheStats.hitRate).toBe(0);

        // Test with hits and misses
        cacheStats.totalHits = 75;
        cacheStats.totalMisses = 25;
        updateHitRateMethod();
        expect(cacheStats.hitRate).toBe(75); // 75% hit rate
      });

      it('should test _updateMemoryUsage method directly', () => {
        const updateMemoryUsageMethod = (cacheManager as any)._updateMemoryUsage.bind(cacheManager);
        const l1Cache = (cacheManager as any)._l1Cache;

        // Add some entries with known sizes
        l1Cache.set('test1', { key: 'test1', size: 100 });
        l1Cache.set('test2', { key: 'test2', size: 200 });

        updateMemoryUsageMethod();

        const cacheStats = (cacheManager as any)._cacheStatistics;
        expect(cacheStats.memoryUsage).toBe(300);
      });

      it('should test _evictEntries method directly', async () => {
        const evictEntriesMethod = (cacheManager as any)._evictEntries.bind(cacheManager);

        // Add entries with different access times
        await cacheManager.set('evict1', 'value1');
        await cacheManager.set('evict2', 'value2');
        await cacheManager.set('evict3', 'value3');

        // Access one entry to make it more recently used
        await cacheManager.get('evict2');

        const evictedCount = await evictEntriesMethod(2);
        expect(evictedCount).toBe(2);

        // Verify statistics were updated
        const statistics = await cacheManager.getCacheStatistics();
        expect(statistics.totalEvictions).toBeGreaterThanOrEqual(2);
      });

      it('should test _executeWarmingStrategy method directly', async () => {
        const executeWarmingStrategyMethod = (cacheManager as any)._executeWarmingStrategy.bind(cacheManager);

        const mockStrategy = {
          strategyId: 'test-strategy',
          name: 'Test Strategy',
          enabled: true,
          rules: [
            { pattern: 'test-*', priority: 1, frequency: 'daily', dependencies: [] },
            { pattern: 'cache-*', priority: 2, frequency: 'hourly', dependencies: [] }
          ],
          metrics: {
            totalWarmed: 0,
            successRate: 0,
            avgWarmingTime: 0,
            lastExecution: new Date()
          }
        };

        const warmedCount = await executeWarmingStrategyMethod(mockStrategy);
        expect(warmedCount).toBeGreaterThanOrEqual(0);
        expect(mockStrategy.metrics.totalWarmed).toBeGreaterThanOrEqual(warmedCount);
      });
    });

    describe('Error Path Coverage', () => {
      it('should test validation error paths', async () => {
        // Test _validateCacheManagerHealth with low hit rate
        const validateCacheManagerHealthMethod = (cacheManager as any)._validateCacheManagerHealth.bind(cacheManager);
        const errors: any[] = [];
        const warnings: any[] = [];

        // Set up low hit rate scenario
        const cacheStats = (cacheManager as any)._cacheStatistics;
        cacheStats.totalHits = 10;
        cacheStats.totalMisses = 200;
        cacheStats.hitRate = 4.76; // Low hit rate

        await validateCacheManagerHealthMethod(errors, warnings);
        expect(warnings.length).toBeGreaterThan(0);
        expect(warnings[0].message).toContain('Low cache hit rate');
      });

      it('should test _validateCacheTiers error path', async () => {
        const validateCacheTiersMethod = (cacheManager as any)._validateCacheTiers.bind(cacheManager);
        const errors: any[] = [];
        const warnings: any[] = [];

        // Clear cache tiers to trigger error
        const originalCacheTiers = (cacheManager as any)._cacheTiers;
        (cacheManager as any)._cacheTiers = new Map();

        await validateCacheTiersMethod(errors, warnings);
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].message).toContain('No cache tiers configured');

        // Restore original cache tiers
        (cacheManager as any)._cacheTiers = originalCacheTiers;
      });

      it('should test _validateMemoryUsage warning path', async () => {
        const validateMemoryUsageMethod = (cacheManager as any)._validateMemoryUsage.bind(cacheManager);
        const errors: any[] = [];
        const warnings: any[] = [];

        // Set high memory usage
        const cacheStats = (cacheManager as any)._cacheStatistics;
        const maxMemory = (cacheManager as any)._cacheConfig.MAX_CACHE_SIZE_MB * 1024 * 1024;
        cacheStats.memoryUsage = maxMemory * 0.95; // 95% usage

        await validateMemoryUsageMethod(errors, warnings);
        expect(warnings.length).toBeGreaterThan(0);
        expect(warnings[0].message).toContain('High memory usage');
      });

      it('should test _ensureCacheCapacity eviction trigger', async () => {
        const ensureCacheCapacityMethod = (cacheManager as any)._ensureCacheCapacity.bind(cacheManager);

        // Mock cache at capacity
        const originalConfig = (cacheManager as any)._cacheConfig;
        (cacheManager as any)._cacheConfig = {
          ...originalConfig,
          MAX_CACHE_ENTRIES: 5,
          EVICTION_THRESHOLD: 0.8
        };

        // Fill cache to trigger eviction
        const l1Cache = (cacheManager as any)._l1Cache;
        for (let i = 0; i < 6; i++) {
          l1Cache.set(`capacity-test-${i}`, {
            key: `capacity-test-${i}`,
            value: `value-${i}`,
            lastAccessed: new Date(Date.now() - i * 1000) // Different access times
          });
        }

        await ensureCacheCapacityMethod();

        // Verify method executed successfully
        expect(ensureCacheCapacityMethod).toBeDefined();
        expect(l1Cache.size).toBeGreaterThanOrEqual(0);

        // Restore original config
        (cacheManager as any)._cacheConfig = originalConfig;
      });

      it('should test _performCacheOptimization comprehensive path', async () => {
        const performCacheOptimizationMethod = (cacheManager as any)._performCacheOptimization.bind(cacheManager);

        // Set up scenario that triggers all optimization paths
        const originalConfig = (cacheManager as any)._cacheConfig;
        (cacheManager as any)._cacheConfig = {
          ...originalConfig,
          MAX_CACHE_ENTRIES: 10
        };

        // Add entries to trigger optimization
        for (let i = 0; i < 8; i++) {
          await cacheManager.set(`optimize-test-${i}`, `value-${i}`);
        }

        await expect(performCacheOptimizationMethod()).resolves.not.toThrow();

        // Restore original config
        (cacheManager as any)._cacheConfig = originalConfig;
      });
    });

    describe('Constructor and Service Methods Coverage', () => {
      it('should test getServiceName method', () => {
        const serviceName = (cacheManager as any).getServiceName();
        expect(serviceName).toBe('governance-rule-cache-manager');
      });

      it('should test getServiceVersion method', () => {
        const serviceVersion = (cacheManager as any).getServiceVersion();
        expect(serviceVersion).toBe('1.0.0');
      });

      it('should test private initialization methods', async () => {
        const manager = new GovernanceRuleCacheManager();

        // Test individual initialization methods
        await expect((manager as any)._initializeCacheStatistics()).resolves.not.toThrow();
        await expect((manager as any)._initializeCacheTiers()).resolves.not.toThrow();
        await expect((manager as any)._loadCacheConfiguration()).resolves.not.toThrow();

        await manager.shutdown();
      });

      it('should test private maintenance methods', async () => {
        // Test maintenance methods
        await expect((cacheManager as any)._persistCacheState()).resolves.not.toThrow();
        await expect((cacheManager as any)._updateCacheConfiguration({})).resolves.not.toThrow();
        await expect((cacheManager as any)._applyCacheConfiguration()).resolves.not.toThrow();
      });
    });
  });
});
