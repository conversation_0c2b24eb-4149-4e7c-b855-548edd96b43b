/**
 * @file GovernanceRuleAuditLogger Unit Tests
 * @filepath server/src/platform/governance/rule-management/infrastructure/__tests__/GovernanceRuleAuditLogger.test.ts
 * @description Comprehensive unit tests for GovernanceRuleAuditLogger with surgical precision testing
 * @coverage-target 95%+ across Statements, Branches, Functions, and Lines
 * @testing-approach Enterprise-grade testing with realistic business scenarios
 */

import { GovernanceRuleAuditLogger } from '../GovernanceRuleAuditLogger';
import { getTimerCoordinator } from '../../../../../../../shared/src/base/TimerCoordinationService';
import {
  TAuditEntry,
  TAuditConfiguration
} from '../../../../../../../shared/src/types/platform/governance/rule-management-types';
import * as crypto from 'crypto';

// Mock dependencies
jest.mock('../../../../../../../shared/src/base/TimerCoordinationService');

// Mock crypto module properly
jest.mock('crypto', () => ({
  randomBytes: jest.fn().mockReturnValue(Buffer.from('test1234')),
  createHash: jest.fn().mockReturnValue({
    update: jest.fn().mockReturnThis(),
    digest: jest.fn().mockReturnValue('mock-hash-value')
  })
}));

describe('GovernanceRuleAuditLogger', () => {
  let auditLogger: GovernanceRuleAuditLogger;
  let mockTimerCoordinator: any;

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup timer coordinator mock
    mockTimerCoordinator = {
      createCoordinatedInterval: jest.fn(),
      clearCoordinatedInterval: jest.fn()
    };
    (getTimerCoordinator as jest.Mock).mockReturnValue(mockTimerCoordinator);

    // Reset crypto mocks to default working state
    const crypto = require('crypto');
    crypto.randomBytes = jest.fn().mockReturnValue(Buffer.from('test1234'));
    crypto.createHash = jest.fn().mockReturnValue({
      update: jest.fn().mockReturnThis(),
      digest: jest.fn().mockReturnValue('mock-hash-value')
    });

    // Create fresh instance
    auditLogger = new GovernanceRuleAuditLogger();
    await auditLogger.initialize();
  });

  afterEach(async () => {
    if (auditLogger) {
      await auditLogger.shutdown();
    }
  });

  describe('Constructor and Initialization', () => {
    test('should initialize with correct default configuration', () => {
      expect(auditLogger).toBeInstanceOf(GovernanceRuleAuditLogger);
      expect(auditLogger['_componentType']).toBe('governance-rule-audit-logger');
      expect(auditLogger['_version']).toBe('1.0.0');
    });

    test('should initialize logger metrics with zero values', () => {
      const metrics = auditLogger['_loggerMetrics'];
      expect(metrics.totalEntriesLogged).toBe(0);
      expect(metrics.totalEntriesFlushed).toBe(0);
      expect(metrics.totalIntegrityChecks).toBe(0);
      expect(metrics.totalCorruptedEntries).toBe(0);
      expect(metrics.errorCount).toBe(0);
    });

    test('should initialize audit configuration with compliance standards', () => {
      const config = auditLogger['_auditConfiguration'];
      expect(config.compliance.regulatoryStandards).toEqual(['SOX', 'GDPR', 'HIPAA']);
      expect(config.security.encryptionEnabled).toBe(true);
      expect(config.security.integrityCheckEnabled).toBe(true);
    });

    test('should setup timer coordination during initialization', async () => {
      expect(mockTimerCoordinator.createCoordinatedInterval).toHaveBeenCalledWith(
        expect.any(Function),
        30000,
        'GovernanceRuleAuditLogger',
        'audit-flush'
      );
    });
  });

  describe('Core Audit Logging', () => {
    test('should log audit entry successfully', async () => {
      const auditEntry: TAuditEntry = {
        level: 'info',
        category: 'rule-execution',
        source: 'test-source',
        action: 'test-action',
        actor: { systemId: 'test-system', service: 'test-service' },
        target: { type: 'test-target', id: 'target-1', name: 'Test Target' },
        result: { status: 'success', message: 'Test successful' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['test', 'audit'],
        metadata: { testData: 'test-value' }
      };

      const auditId = await auditLogger.logAudit(auditEntry);

      expect(auditId).toMatch(/^audit-\d+-[a-f0-9]+$/);
      expect(auditLogger['_loggerMetrics'].totalEntriesLogged).toBe(1);
      expect(auditLogger['_auditBuffer']).toHaveLength(1);
    });

    test('should handle audit logging errors and increment error count', async () => {
      // Mock crypto to throw error
      const crypto = require('crypto');
      crypto.createHash = jest.fn().mockImplementation(() => {
        throw new Error('Hash calculation failed');
      });

      const auditEntry: TAuditEntry = {
        level: 'info',
        category: 'rule-execution',
        source: 'test-source',
        action: 'test-action',
        actor: { systemId: 'test-system', service: 'test-service' },
        target: { type: 'test-target', id: 'target-1' },
        result: { status: 'success' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['test'],
        metadata: {}
      };

      await expect(auditLogger.logAudit(auditEntry)).rejects.toThrow('Hash calculation failed');
      expect(auditLogger['_loggerMetrics'].errorCount).toBe(1);
    });

    test('should flush buffer when buffer size limit reached', async () => {
      // Set small buffer size for testing
      auditLogger['_auditConfiguration'].performance.bufferSize = 1;

      const auditEntry: TAuditEntry = {
        level: 'info',
        category: 'rule-execution',
        source: 'test-source',
        action: 'test-action',
        actor: { systemId: 'test-system', service: 'test-service' },
        target: { type: 'test-target', id: 'target-1' },
        result: { status: 'success' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['test'],
        metadata: {}
      };

      // Add first entry - should trigger immediate flush due to buffer size = 1
      await auditLogger.logAudit(auditEntry);
      expect(auditLogger['_auditBuffer']).toHaveLength(0);
      expect(auditLogger['_auditLog'].size).toBe(1);
      expect(auditLogger['_loggerMetrics'].totalEntriesFlushed).toBe(1);
    });
  });

  describe('Rule Execution Logging', () => {
    test('should log successful rule execution', async () => {
      const execution = {
        status: 'completed',
        executedBy: 'test-user',
        ruleName: 'Test Rule',
        executionTime: 150,
        resourceUsage: { cpu: 10, memory: 20 },
        violations: [],
        recommendations: ['Optimize performance']
      };

      const auditId = await auditLogger.logRuleExecution('rule-123', execution);

      expect(auditId).toMatch(/^audit-\d+-[a-f0-9]+$/);
      expect(auditLogger['_auditBuffer']).toHaveLength(1);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.level).toBe('info');
      expect(loggedEntry.category).toBe('rule-execution');
      expect(loggedEntry.result.status).toBe('success');
    });

    test('should log failed rule execution with error level', async () => {
      const execution = {
        status: 'failed',
        executedBy: 'test-user',
        ruleName: 'Test Rule',
        message: 'Rule execution failed',
        violations: ['Critical violation found']
      };

      const auditId = await auditLogger.logRuleExecution('rule-456', execution);

      expect(auditId).toMatch(/^audit-\d+-[a-f0-9]+$/);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.level).toBe('error');
      expect(loggedEntry.result.status).toBe('failure');
      expect(loggedEntry.result.details.violations).toEqual(['Critical violation found']);
    });

    test('should handle rule execution logging errors', async () => {
      // Mock _createAuditLogEntry to throw error
      const originalMethod = auditLogger['_createAuditLogEntry'];
      auditLogger['_createAuditLogEntry'] = jest.fn().mockRejectedValue(new Error('Creation failed'));

      const execution = { status: 'completed' };

      await expect(auditLogger.logRuleExecution('rule-789', execution)).rejects.toThrow('Creation failed');

      // Restore original method
      auditLogger['_createAuditLogEntry'] = originalMethod;
    });

    test('should use default values for missing execution properties', async () => {
      const execution = {}; // Empty execution object

      const auditId = await auditLogger.logRuleExecution('rule-default', execution);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.actor.systemId).toBe('governance-system');
      expect(loggedEntry.target.name).toBe('rule-default');
      expect(loggedEntry.result.message).toBe('Rule execution undefined');
    });
  });

  describe('Audit Search and Filtering', () => {
    beforeEach(async () => {
      // Clear existing data
      auditLogger['_auditLog'].clear();
      auditLogger['_auditBuffer'].length = 0;

      // Add test entries to audit log
      const testEntries = [
        {
          level: 'info', category: 'rule-execution', source: 'test-source-1',
          action: 'test-action-1', actor: { systemId: 'system-1', service: 'service-1' },
          target: { type: 'target-1', id: 'id-1' }, result: { status: 'success' },
          context: { environment: 'test', version: '1.0.0' },
          security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
          tags: ['tag1', 'common'], metadata: { data: 'value1' }
        },
        {
          level: 'error', category: 'compliance-check', source: 'test-source-2',
          action: 'test-action-2', actor: { systemId: 'system-2', service: 'service-2' },
          target: { type: 'target-2', id: 'id-2' }, result: { status: 'failure', message: 'Failed test' },
          context: { environment: 'test', version: '1.0.0' },
          security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
          tags: ['tag2', 'common'], metadata: { data: 'value2' }
        }
      ];

      // Set buffer size to 1 to force immediate flush
      auditLogger['_auditConfiguration'].performance.bufferSize = 1;

      for (const entry of testEntries) {
        await auditLogger.logAudit(entry as TAuditEntry);
      }
    });

    test('should search audit logs with level filter', async () => {
      // Ensure we have test data by checking and adding if needed
      if (auditLogger['_auditLog'].size === 0) {
        const testEntry: TAuditEntry = {
          level: 'info', category: 'rule-execution', source: 'test-source-1',
          action: 'test-action-1', actor: { systemId: 'system-1', service: 'service-1' },
          target: { type: 'target-1', id: 'id-1' }, result: { status: 'success' },
          context: { environment: 'test', version: '1.0.0' },
          security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
          tags: ['tag1', 'common'], metadata: { data: 'value1' }
        };

        auditLogger['_auditConfiguration'].performance.bufferSize = 1;
        await auditLogger.logAudit(testEntry);
      }

      const results = await auditLogger.searchAuditLogs({
        levels: ['info']
      });

      expect(results.length).toBeGreaterThanOrEqual(0);
      if (results.length > 0) {
        expect(results[0].level).toBe('info');
      }
    });

    test('should search audit logs with category filter', async () => {
      const results = await auditLogger.searchAuditLogs({
        categories: ['compliance-check']
      });

      expect(results).toHaveLength(1);
      expect(results[0].category).toBe('compliance-check');
    });

    test('should search audit logs with time range filter', async () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 3600000);

      const results = await auditLogger.searchAuditLogs({
        startTime: oneHourAgo,
        endTime: now
      });

      expect(results.length).toBeGreaterThan(0);
      results.forEach(entry => {
        expect(entry.timestamp.getTime()).toBeGreaterThanOrEqual(oneHourAgo.getTime());
        expect(entry.timestamp.getTime()).toBeLessThanOrEqual(now.getTime());
      });
    });

    test('should search audit logs with status filter', async () => {
      const results = await auditLogger.searchAuditLogs({
        statuses: ['failure']
      });

      expect(results).toHaveLength(1);
      expect(results[0].result.status).toBe('failure');
    });

    test('should search audit logs with tag filter', async () => {
      // Always create fresh test data for this specific test
      auditLogger['_auditConfiguration'].performance.bufferSize = 1;

      const testEntry: TAuditEntry = {
        level: 'info', category: 'rule-execution', source: 'test-source-1',
        action: 'test-action-1', actor: { systemId: 'system-1', service: 'service-1' },
        target: { type: 'target-1', id: 'id-1' }, result: { status: 'success' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['tag1', 'common'], metadata: { data: 'value1' }
      };

      await auditLogger.logAudit(testEntry);

      const results = await auditLogger.searchAuditLogs({
        tags: ['tag1']
      });

      expect(results.length).toBeGreaterThan(0);
      expect(results[0].tags).toContain('tag1');
      // Verify all results contain the tag
      results.forEach(result => {
        expect(result.tags).toContain('tag1');
      });
    });

    test('should search audit logs with text search', async () => {
      const results = await auditLogger.searchAuditLogs({
        textSearch: 'Failed test'
      });

      expect(results).toHaveLength(1);
      expect(results[0].result.message).toContain('Failed test');
    });

    test('should search audit logs with text search in metadata', async () => {
      const results = await auditLogger.searchAuditLogs({
        textSearch: 'value2'
      });

      expect(results).toHaveLength(1);
      expect(results[0].metadata.data).toBe('value2');
    });

    test('should sort search results by timestamp descending', async () => {
      // Ensure we have multiple entries for sorting
      if (auditLogger['_auditLog'].size < 2) {
        auditLogger['_auditConfiguration'].performance.bufferSize = 1;

        const testEntry1: TAuditEntry = {
          level: 'info', category: 'rule-execution', source: 'test-source-1',
          action: 'test-action-1', actor: { systemId: 'system-1', service: 'service-1' },
          target: { type: 'target-1', id: 'id-1' }, result: { status: 'success' },
          context: { environment: 'test', version: '1.0.0' },
          security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
          tags: ['tag1'], metadata: { data: 'value1' }
        };

        const testEntry2: TAuditEntry = {
          level: 'error', category: 'compliance-check', source: 'test-source-2',
          action: 'test-action-2', actor: { systemId: 'system-2', service: 'service-2' },
          target: { type: 'target-2', id: 'id-2' }, result: { status: 'failure' },
          context: { environment: 'test', version: '1.0.0' },
          security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
          tags: ['tag2'], metadata: { data: 'value2' }
        };

        await auditLogger.logAudit(testEntry1);
        // Create entries with slightly different timestamps by manually setting them
        await auditLogger.logAudit(testEntry2);
      }

      const results = await auditLogger.searchAuditLogs({
        sortBy: 'timestamp',
        sortOrder: 'desc'
      });

      expect(results.length).toBeGreaterThanOrEqual(1);
      if (results.length > 1) {
        for (let i = 1; i < results.length; i++) {
          expect(results[i-1].timestamp.getTime()).toBeGreaterThanOrEqual(results[i].timestamp.getTime());
        }
      }
    });

    test('should sort search results by timestamp ascending', async () => {
      // Ensure we have multiple entries for sorting
      if (auditLogger['_auditLog'].size < 2) {
        auditLogger['_auditConfiguration'].performance.bufferSize = 1;

        const testEntry1: TAuditEntry = {
          level: 'info', category: 'rule-execution', source: 'test-source-1',
          action: 'test-action-1', actor: { systemId: 'system-1', service: 'service-1' },
          target: { type: 'target-1', id: 'id-1' }, result: { status: 'success' },
          context: { environment: 'test', version: '1.0.0' },
          security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
          tags: ['tag1'], metadata: { data: 'value1' }
        };

        const testEntry2: TAuditEntry = {
          level: 'error', category: 'compliance-check', source: 'test-source-2',
          action: 'test-action-2', actor: { systemId: 'system-2', service: 'service-2' },
          target: { type: 'target-2', id: 'id-2' }, result: { status: 'failure' },
          context: { environment: 'test', version: '1.0.0' },
          security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
          tags: ['tag2'], metadata: { data: 'value2' }
        };

        await auditLogger.logAudit(testEntry1);
        // Create entries with slightly different timestamps by manually setting them
        await auditLogger.logAudit(testEntry2);
      }

      const results = await auditLogger.searchAuditLogs({
        sortBy: 'timestamp',
        sortOrder: 'asc'
      });

      expect(results.length).toBeGreaterThanOrEqual(1);
      if (results.length > 1) {
        for (let i = 1; i < results.length; i++) {
          expect(results[i-1].timestamp.getTime()).toBeLessThanOrEqual(results[i].timestamp.getTime());
        }
      }
    });

    test('should apply pagination to search results', async () => {
      const results = await auditLogger.searchAuditLogs({
        limit: 1,
        offset: 0
      });

      expect(results).toHaveLength(1);
    });

    test('should handle search errors gracefully', async () => {
      // Mock Array.from to throw error
      const originalArrayFrom = Array.from;
      Array.from = jest.fn().mockImplementation(() => {
        throw new Error('Array conversion failed');
      });

      await expect(auditLogger.searchAuditLogs({})).rejects.toThrow('Array conversion failed');

      // Restore original method
      Array.from = originalArrayFrom;
    });
  });

  describe('Governance Event Logging', () => {
    test('should log governance event with default values', async () => {
      const event = {
        type: 'policy-update',
        data: { policyId: 'policy-123' }
      };

      const auditId = await auditLogger.logGovernanceEvent(event);

      expect(auditId).toMatch(/^audit-\d+-[a-f0-9]+$/);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.level).toBe('info');
      expect(loggedEntry.category).toBe('system-event');
      expect(loggedEntry.metadata.eventType).toBe('policy-update');
    });

    test('should log governance event with custom properties', async () => {
      const event = {
        type: 'rule-violation',
        level: 'error',
        category: 'compliance-check',
        source: 'compliance-engine',
        action: 'violation-detected',
        actor: { systemId: 'compliance-system', service: 'violation-detector' },
        target: { type: 'rule', id: 'rule-456', name: 'Security Rule' },
        result: { status: 'failure', message: 'Rule violation detected' },
        context: { sessionId: 'session-123', environment: 'production' },
        security: { classification: 'confidential', sensitivity: 'high' },
        tags: ['violation', 'security'],
        metadata: { severity: 'critical', impact: 'high' }
      };

      const auditId = await auditLogger.logGovernanceEvent(event);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.level).toBe('error');
      expect(loggedEntry.category).toBe('compliance-check');
      expect(loggedEntry.source).toBe('compliance-engine');
      expect(loggedEntry.metadata.severity).toBe('critical');
    });

    test('should handle governance event logging errors', async () => {
      // Mock logAudit to throw error
      const originalLogAudit = auditLogger.logAudit;
      auditLogger.logAudit = jest.fn().mockRejectedValue(new Error('Audit logging failed'));

      const event = { type: 'test-event' };

      await expect(auditLogger.logGovernanceEvent(event)).rejects.toThrow('Audit logging failed');

      // Restore original method
      auditLogger.logAudit = originalLogAudit;
    });
  });

  describe('Compliance Check Logging', () => {
    test('should log successful compliance check', async () => {
      const complianceCheck = {
        standard: 'GDPR',
        passed: true,
        score: 95,
        framework: 'privacy-framework',
        targetId: 'data-processor-1',
        targetName: 'Data Processing System',
        checkDuration: 2500
      };

      const auditId = await auditLogger.logComplianceCheck(complianceCheck);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.category).toBe('compliance-check');
      expect(loggedEntry.result.status).toBe('success');
      expect(loggedEntry.result.details.standard).toBe('GDPR');
      expect(loggedEntry.result.details.score).toBe(95);
      expect(loggedEntry.tags).toContain('GDPR');
    });

    test('should log failed compliance check', async () => {
      const complianceCheck = {
        standard: 'SOX',
        passed: false,
        score: 45,
        violations: ['Missing audit trail', 'Insufficient access controls'],
        recommendations: ['Implement comprehensive logging', 'Review access permissions']
      };

      const auditId = await auditLogger.logComplianceCheck(complianceCheck);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.result.status).toBe('failure');
      expect(loggedEntry.result.details.violations).toEqual(['Missing audit trail', 'Insufficient access controls']);
      expect(loggedEntry.result.details.recommendations).toEqual(['Implement comprehensive logging', 'Review access permissions']);
    });

    test('should use default values for missing compliance check properties', async () => {
      const complianceCheck = {}; // Empty compliance check

      const auditId = await auditLogger.logComplianceCheck(complianceCheck);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.level).toBe('info');
      expect(loggedEntry.actor.systemId).toBe('compliance-system');
      expect(loggedEntry.target.name).toBe('Compliance Target');
      expect(loggedEntry.tags).toContain('unknown-standard');
    });
  });

  describe('Authority Validation Logging', () => {
    test('should log successful authority validation', async () => {
      const authorityValidation = {
        subject: 'user-123',
        authorized: true,
        requestedAction: 'read-sensitive-data',
        resource: 'customer-database',
        permissions: ['read', 'query'],
        authorityLevel: 'manager',
        validationDuration: 150
      };

      const auditId = await auditLogger.logAuthorityValidation(authorityValidation);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.category).toBe('authority-check');
      expect(loggedEntry.result.status).toBe('success');
      expect(loggedEntry.result.details.subject).toBe('user-123');
      expect(loggedEntry.result.details.authorityLevel).toBe('manager');
      expect(loggedEntry.tags).toContain('granted');
    });

    test('should log failed authority validation', async () => {
      const authorityValidation = {
        subject: 'user-456',
        authorized: false,
        requestedAction: 'delete-records',
        resource: 'audit-logs',
        message: 'Insufficient privileges for requested action'
      };

      const auditId = await auditLogger.logAuthorityValidation(authorityValidation);

      const loggedEntry = auditLogger['_auditBuffer'][0];
      expect(loggedEntry.result.status).toBe('failure');
      expect(loggedEntry.result.message).toBe('Insufficient privileges for requested action');
      expect(loggedEntry.tags).toContain('denied');
      expect(loggedEntry.security.classification).toBe('restricted');
      expect(loggedEntry.security.sensitivity).toBe('critical');
    });
  });

  describe('Audit Report Generation', () => {
    beforeEach(async () => {
      // Add test data for reporting
      const testEntry: TAuditEntry = {
        level: 'info', category: 'rule-execution', source: 'test-source',
        action: 'test-action', actor: { systemId: 'test-system', service: 'test-service' },
        target: { type: 'test-target', id: 'target-1' }, result: { status: 'success' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['test'], metadata: {}
      };

      await auditLogger.logAudit(testEntry);
      await auditLogger['_flushAuditBuffer']();
    });

    test('should generate audit report with default configuration', async () => {
      const reportConfig = {};

      const report = await auditLogger.generateAuditReport(reportConfig);

      expect(report.reportId).toMatch(/^report-\d+-[a-f0-9]+$/);
      expect(report.title).toBe('Audit Report');
      expect(report.description).toBe('Generated audit report');
      expect(report.generatedBy).toBe('system');
      expect(report.summary.totalEntries).toBeGreaterThan(0);
      expect(report.metadata.dataIntegrity).toBe(true);
    });

    test('should generate audit report with custom configuration', async () => {
      const reportConfig = {
        title: 'Custom Security Report',
        description: 'Security audit report for Q1',
        generatedBy: 'security-team',
        criteria: { levels: ['info'] },
        format: 'pdf',
        includeSensitiveData: true,
        maxEntries: 5000
      };

      const report = await auditLogger.generateAuditReport(reportConfig);

      expect(report.title).toBe('Custom Security Report');
      expect(report.description).toBe('Security audit report for Q1');
      expect(report.generatedBy).toBe('security-team');
      expect(report.format).toBe('pdf');
      expect(report.includeSensitiveData).toBe(true);
      expect(report.maxEntries).toBe(5000);
    });

    test('should handle report generation errors', async () => {
      // Mock searchAuditLogs to throw error
      const originalSearch = auditLogger.searchAuditLogs;
      auditLogger.searchAuditLogs = jest.fn().mockRejectedValue(new Error('Search failed'));

      await expect(auditLogger.generateAuditReport({})).rejects.toThrow('Search failed');

      // Restore original method
      auditLogger.searchAuditLogs = originalSearch;
    });
  });

  describe('Integrity Check Operations', () => {
    beforeEach(async () => {
      // Add test entries for integrity checking
      const testEntry: TAuditEntry = {
        level: 'info', category: 'rule-execution', source: 'test-source',
        action: 'test-action', actor: { systemId: 'test-system', service: 'test-service' },
        target: { type: 'test-target', id: 'target-1' }, result: { status: 'success' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['test'], metadata: {}
      };

      await auditLogger.logAudit(testEntry);
      await auditLogger['_flushAuditBuffer']();
    });

    test('should perform integrity check successfully', async () => {
      const integrityCheck = await auditLogger.performIntegrityCheck();

      expect(integrityCheck.checkId).toMatch(/^integrity-\d+-[a-f0-9]+$/);
      expect(integrityCheck.totalEntries).toBeGreaterThan(0);
      expect(integrityCheck.validEntries).toBeGreaterThan(0);
      expect(integrityCheck.integrityScore).toBe(100);
      expect(auditLogger['_loggerMetrics'].totalIntegrityChecks).toBe(1);
    });

    test('should perform integrity check with time range', async () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 3600000);

      const integrityCheck = await auditLogger.performIntegrityCheck(oneHourAgo, now);

      // Just verify that the integrity check was performed with time range parameters
      expect(integrityCheck.checkId).toMatch(/^integrity-\d+-[a-f0-9]+$/);
      expect(integrityCheck.totalEntries).toBeGreaterThanOrEqual(0);
      expect(integrityCheck.validEntries).toBeGreaterThanOrEqual(0);
    });

    test('should detect corrupted entries during integrity check', async () => {
      // Manually corrupt an entry in the audit log
      const entries = Array.from(auditLogger['_auditLog'].values());
      if (entries.length > 0) {
        entries[0].integrity.hash = 'corrupted-hash';
      }

      const integrityCheck = await auditLogger.performIntegrityCheck();

      expect(integrityCheck.corruptedEntries).toBeGreaterThan(0);
      expect(integrityCheck.integrityScore).toBeLessThan(100);
      expect(integrityCheck.issues.length).toBeGreaterThan(0);
      expect(integrityCheck.issues[0].type).toBe('corruption');
    });

    test('should handle integrity check errors', async () => {
      // Mock _performIntegrityCheck to throw error
      const originalMethod = auditLogger['_performIntegrityCheck'];
      auditLogger['_performIntegrityCheck'] = jest.fn().mockRejectedValue(new Error('Integrity check failed'));

      await expect(auditLogger.performIntegrityCheck()).rejects.toThrow('Integrity check failed');

      // Restore original method
      auditLogger['_performIntegrityCheck'] = originalMethod;
    });
  });

  describe('Audit Trail Operations', () => {
    beforeEach(async () => {
      // Add test data
      const testEntry: TAuditEntry = {
        level: 'info', category: 'rule-execution', source: 'test-source',
        action: 'test-action', actor: { systemId: 'test-system', service: 'test-service' },
        target: { type: 'test-target', id: 'target-1' }, result: { status: 'success' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['test'], metadata: {}
      };

      await auditLogger.logAudit(testEntry);
      await auditLogger['_flushAuditBuffer']();
    });

    test('should get audit trail with default filters', async () => {
      const filters = {};

      const auditTrail = await auditLogger.getAuditTrail(filters);

      expect(auditTrail.trailId).toMatch(/^trail-\d+-[a-f0-9]+$/);
      expect(auditTrail.entries.length).toBeGreaterThan(0);
      expect(auditTrail.summary.totalEntries).toBeGreaterThan(0);
      expect(auditTrail.metadata.maxEntries).toBe(1000);
    });

    test('should get audit trail with custom filters', async () => {
      const filters = {
        startTime: new Date(Date.now() - 3600000),
        endTime: new Date(),
        levels: ['info'],
        limit: 50,
        offset: 0,
        sortBy: 'timestamp',
        sortOrder: 'asc',
        requestedBy: 'test-user'
      };

      const auditTrail = await auditLogger.getAuditTrail(filters);

      expect(auditTrail.metadata.requestedBy).toBe('test-user');
      expect(auditTrail.metadata.maxEntries).toBe(50);
    });
  });

  describe('Audit Log Archival', () => {
    beforeEach(async () => {
      // Add old entries for archival testing
      const oldEntry: TAuditEntry = {
        level: 'info', category: 'rule-execution', source: 'test-source',
        action: 'old-action', actor: { systemId: 'test-system', service: 'test-service' },
        target: { type: 'test-target', id: 'old-target' }, result: { status: 'success' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['old'], metadata: {}
      };

      await auditLogger.logAudit(oldEntry);
      await auditLogger['_flushAuditBuffer']();

      // Manually set old timestamp
      const entries = Array.from(auditLogger['_auditLog'].values());
      if (entries.length > 0) {
        entries[0].timestamp = new Date(Date.now() - 400 * 24 * 60 * 60 * 1000); // 400 days ago
      }
    });

    test('should archive old audit logs successfully', async () => {
      const retentionPolicy = {
        archiveAfterDays: 365,
        deleteAfterArchive: false
      };

      await auditLogger.archiveAuditLogs(retentionPolicy);

      // Should have logged the archival event
      expect(auditLogger['_auditBuffer'].length).toBeGreaterThan(0);
      const archivalEvent = auditLogger['_auditBuffer'].find(entry =>
        entry.metadata.eventType === 'audit-archival'
      );
      expect(archivalEvent).toBeDefined();
    });

    test('should archive and delete old logs when specified', async () => {
      const initialLogSize = auditLogger['_auditLog'].size;

      const retentionPolicy = {
        archiveAfterDays: 365,
        deleteAfterArchive: true
      };

      await auditLogger.archiveAuditLogs(retentionPolicy);

      // Log size should be reduced if old entries were deleted
      expect(auditLogger['_auditLog'].size).toBeLessThanOrEqual(initialLogSize);
    });

    test('should handle archival errors gracefully', async () => {
      // Mock _archiveEntry to throw error
      const originalArchive = auditLogger['_archiveEntry'];
      auditLogger['_archiveEntry'] = jest.fn().mockRejectedValue(new Error('Archive failed'));

      const retentionPolicy = { archiveAfterDays: 365 };

      // Should not throw error, but handle it gracefully
      await expect(auditLogger.archiveAuditLogs(retentionPolicy)).resolves.not.toThrow();

      // Restore original method
      auditLogger['_archiveEntry'] = originalArchive;
    });
  });

  describe('Configuration Management', () => {
    test('should configure audit settings successfully', async () => {
      const configuration: TAuditConfiguration = {
        retention: {
          days: 2000,
          archiveThresholdDays: 730,
          deleteAfterArchive: true
        },
        security: {
          encryptionEnabled: true,
          compressionEnabled: true,
          integrityCheckEnabled: true,
          signatureRequired: true
        },
        performance: {
          bufferSize: 2000,
          flushIntervalMs: 60000,
          batchSize: 200,
          compressionLevel: 9
        },
        compliance: {
          regulatoryStandards: ['SOX', 'GDPR', 'HIPAA', 'PCI-DSS'],
          requiredFields: ['timestamp', 'actor', 'action', 'target', 'result', 'context'],
          sensitiveDataHandling: 'encrypt-and-hash'
        }
      };

      await auditLogger.configureAudit(configuration);

      const updatedConfig = auditLogger['_auditConfiguration'];
      expect(updatedConfig.retention.days).toBe(2000);
      expect(updatedConfig.performance.bufferSize).toBe(2000);
      expect(updatedConfig.compliance.regulatoryStandards).toContain('PCI-DSS');
    });

    test('should handle configuration errors', async () => {
      // Mock _updateAuditConfiguration to throw error
      const originalUpdate = auditLogger['_updateAuditConfiguration'];
      auditLogger['_updateAuditConfiguration'] = jest.fn().mockRejectedValue(new Error('Config update failed'));

      const configuration = {} as TAuditConfiguration;

      await expect(auditLogger.configureAudit(configuration)).rejects.toThrow('Config update failed');

      // Restore original method
      auditLogger['_updateAuditConfiguration'] = originalUpdate;
    });
  });

  describe('Metrics and Monitoring', () => {
    test('should return comprehensive metrics', async () => {
      // Add some test data to generate metrics
      const testEntry: TAuditEntry = {
        level: 'info', category: 'rule-execution', source: 'test-source',
        action: 'test-action', actor: { systemId: 'test-system', service: 'test-service' },
        target: { type: 'test-target', id: 'target-1' }, result: { status: 'success' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['test'], metadata: {}
      };

      await auditLogger.logAudit(testEntry);

      const metrics = await auditLogger.getMetrics();

      expect(metrics.custom.totalEntriesLogged).toBeGreaterThan(0);
      expect(metrics.custom.bufferSize).toBeGreaterThan(0);
      expect(metrics.custom.auditLogSize).toBeGreaterThanOrEqual(0);
      expect(metrics.custom.integrityChainLength).toBeGreaterThan(0);
    });

    test('should handle metrics errors', async () => {
      // Mock super.getMetrics to throw error
      const originalGetMetrics = Object.getPrototypeOf(Object.getPrototypeOf(auditLogger)).getMetrics;
      Object.getPrototypeOf(Object.getPrototypeOf(auditLogger)).getMetrics = jest.fn().mockRejectedValue(new Error('Metrics failed'));

      await expect(auditLogger.getMetrics()).rejects.toThrow('Metrics failed');

      // Restore original method
      Object.getPrototypeOf(Object.getPrototypeOf(auditLogger)).getMetrics = originalGetMetrics;
    });
  });

  describe('Service Validation', () => {
    test('should validate service successfully with no errors', async () => {
      const validation = await auditLogger['doValidate']();

      expect(validation.validationId).toMatch(/^gov-audit-logger-val-\d+$/);
      expect(validation.componentId).toBe('governance-rule-audit-logger');
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBeGreaterThan(90);
    });

    test('should detect high error count and add warning', async () => {
      // Simulate high error count
      auditLogger['_loggerMetrics'].errorCount = 60;

      const validation = await auditLogger['doValidate']();

      expect(validation.warnings.length).toBeGreaterThan(0);
      expect(validation.warnings[0]).toContain('High error count: 60');
    });

    test('should detect missing integrity chain and add error', async () => {
      // Clear integrity chain but keep audit log entries
      auditLogger['_integrityChain'].length = 0;
      auditLogger['_auditLog'].set('test-entry', {} as any);

      const validation = await auditLogger['doValidate']();

      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]).toContain('Integrity chain missing despite audit entries');
      expect(validation.status).toBe('invalid');
    });

    test('should warn about missing regulatory standards', async () => {
      // Clear regulatory standards
      auditLogger['_auditConfiguration'].compliance.regulatoryStandards = [];

      const validation = await auditLogger['doValidate']();

      expect(validation.warnings.length).toBeGreaterThan(0);
      expect(validation.warnings.some(w => w.includes('No regulatory standards configured'))).toBe(true);
    });

    test('should handle validation errors', async () => {
      // Mock _validateAuditLoggerHealth to throw error
      const originalValidate = auditLogger['_validateAuditLoggerHealth'];
      auditLogger['_validateAuditLoggerHealth'] = jest.fn().mockRejectedValue(new Error('Validation failed'));

      await expect(auditLogger['doValidate']()).rejects.toThrow('Validation failed');

      // Restore original method
      auditLogger['_validateAuditLoggerHealth'] = originalValidate;
    });
  });

  describe('Private Helper Methods', () => {
    test('should calculate entry hash correctly', async () => {
      const auditId = 'test-audit-id';
      const entry: TAuditEntry = {
        level: 'info', category: 'rule-execution', source: 'test-source',
        action: 'test-action', actor: { systemId: 'test-system', service: 'test-service' },
        target: { type: 'test-target', id: 'target-1' }, result: { status: 'success' },
        context: { environment: 'test', version: '1.0.0' },
        security: { classification: 'internal', sensitivity: 'medium', retention: '7-years' },
        tags: ['test'], metadata: {}
      };

      const hash = await auditLogger['_calculateEntryHash'](auditId, entry);

      expect(hash).toBe('mock-hash-value');
      const crypto = require('crypto');
      expect(crypto.createHash).toHaveBeenCalledWith('sha256');
    });

    test('should flush empty buffer without errors', async () => {
      // Ensure buffer is empty
      auditLogger['_auditBuffer'].length = 0;

      await expect(auditLogger['_flushAuditBuffer']()).resolves.not.toThrow();
    });

    test('should generate category summary correctly', async () => {
      const entries = [
        { category: 'rule-execution' } as any,
        { category: 'rule-execution' } as any,
        { category: 'compliance-check' } as any
      ];

      const summary = auditLogger['_generateCategorySummary'](entries);

      expect(summary['rule-execution']).toBe(2);
      expect(summary['compliance-check']).toBe(1);
    });

    test('should calculate success rate correctly', async () => {
      const entries = [
        { result: { status: 'success' } } as any,
        { result: { status: 'success' } } as any,
        { result: { status: 'failure' } } as any
      ];

      const successRate = auditLogger['_calculateSuccessRate'](entries);

      expect(successRate).toBeCloseTo(66.67, 1);
    });

    test('should return 100% success rate for empty entries', async () => {
      const successRate = auditLogger['_calculateSuccessRate']([]);

      expect(successRate).toBe(100);
    });

    test('should get nested value from object path', async () => {
      const obj = {
        level1: {
          level2: {
            value: 'test-value'
          }
        }
      };

      const value = auditLogger['_getNestedValue'](obj, 'level1.level2.value');

      expect(value).toBe('test-value');
    });

    test('should handle undefined nested value gracefully', async () => {
      const obj = { level1: {} };

      const value = auditLogger['_getNestedValue'](obj, 'level1.missing.value');

      expect(value).toBeUndefined();
    });
  });
});
