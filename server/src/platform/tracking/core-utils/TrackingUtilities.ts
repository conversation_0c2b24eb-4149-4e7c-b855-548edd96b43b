/**
 * @file Tracking Utilities
 * @filepath server/src/platform/tracking/core-utils/TrackingUtilities.ts
 * @task-id T-TSK-03.SUB-03.2.IMP-03
 * @component tracking-tracking-utils
 * @reference foundation-context.UTILITY.001
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/tracking/tracking-management-types
 * @depends-on shared/src/constants/tracking/tracking-management-constants
 * @enables server/src/platform/tracking/core-managers
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type utilities
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/utilities/tracking-utilities.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

// MEMORY SAFETY INTEGRATION - BaseTrackingService inheritance
import { BaseTrackingService } from '../core-data/base/BaseTrackingService';
import {
  TTrackingConfig,
  TValidationResult,
  TValidationError,
  TValidationWarning
} from '../../../../../shared/src/types/platform/tracking/tracking-types';

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import {
  ResilientTimer
} from '../../../../../shared/src/base/utils/ResilientTiming';

import {
  ResilientMetricsCollector
} from '../../../../../shared/src/base/utils/ResilientMetrics';

import {
  IUtilities,
  IHelperService
} from '../../../../../shared/src/interfaces/tracking/tracking-interfaces';
import {
  TManagerConfig,
  TManagerMetrics,
  TFileOperationResult,
  THealthStatus,
  TOperationResult,
  TConfigValidation
} from '../../../../../shared/src/types/tracking/tracking-management-types';
import {
  DEFAULT_MANAGER_CONFIG,
  VALIDATION_RULES,
  PERFORMANCE_THRESHOLDS,
  LOG_LEVELS
} from '../../../../../shared/src/constants/tracking/tracking-management-constants';

/**
 * Tracking Utilities Implementation
 *
 * Provides comprehensive utility functions for tracking management operations
 * including configuration validation, performance monitoring, error handling,
 * and common operations optimization.
 *
 * ✅ MEMORY SAFETY: Extends BaseTrackingService for enterprise-grade resource management
 * ✅ RESILIENT TIMING: Integrated timing infrastructure for utility operations
 *
 * @implements {IUtilities}
 * @implements {IHelperService}
 */
export class TrackingUtilities extends BaseTrackingService implements IUtilities, IHelperService {
  // ============================================================================
  // PRIVATE PROPERTIES
  // ============================================================================

  // RESILIENT TIMING INFRASTRUCTURE - Dual-field pattern
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  /** Utility name identifier */
  private readonly _utilityName: string = 'TrackingUtilities';

  /** Utility version */
  private readonly _version: string = '1.0.0';

  /** Performance cache for optimization */
  private readonly _performanceCache: Map<string, any> = new Map();

  /** Operation counters */
  private readonly _utilityOperationCounters: Map<string, number> = new Map();

  /** Error tracking */
  private readonly _utilityErrors: Array<{ timestamp: string; operation: string; error: any }> = [];

  /**
   * Initialize the Tracking Utilities service
   */
  constructor(config?: Partial<TTrackingConfig>) {
    // ✅ Initialize memory-safe base class with utility-specific resource limits
    super({
      service: {
        name: 'tracking-utilities',
        version: '1.0.0',
        environment: (process.env.NODE_ENV as 'production' | 'development' | 'staging') || 'production',
        timeout: 30000,
        retry: { maxAttempts: 3, delay: 1000, backoffMultiplier: 2, maxDelay: 10000 }
      },
      ...config
    });

    // ✅ RESILIENT TIMING: Initialize timing infrastructure immediately
    // This prevents "Cannot read properties of undefined" errors during shutdown
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: 500, // 500ms for utility operations
      unreliableThreshold: 3,
      estimateBaseline: 10
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['utility_operation', 25],
        ['config_validation', 50],
        ['data_sanitization', 30],
        ['performance_calculation', 75],
        ['id_generation', 5]
      ])
    });
  }

  // ============================================================================
  // BASETRACKINGSERVICE IMPLEMENTATION - Required abstract methods
  // ============================================================================

  /**
   * Get service name for tracking
   */
  protected getServiceName(): string {
    return 'tracking-utilities';
  }

  /**
   * Get service version for tracking
   */
  protected getServiceVersion(): string {
    return '1.0.0';
  }

  /**
   * Initialize service-specific functionality
   * ✅ MEMORY SAFETY: Uses doInitialize() lifecycle hook
   */
  protected async doInitialize(): Promise<void> {
    try {
      // Initialize performance cache with start time
      this._performanceCache.set('startTime', Date.now());

      this.logOperation('doInitialize', 'Tracking utilities initialized successfully');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logError('doInitialize', error);
      throw new Error(`Tracking utilities initialization failed: ${errorMessage}`);
    }
  }

  /**
   * Track service-specific data
   */
  protected async doTrack(data: Record<string, unknown>): Promise<void> {
    this.logOperation('doTrack', 'Tracking utilities data', data);
  }

  /**
   * Validate service-specific data
   * ✅ REQUIRED: Abstract method implementation from BaseTrackingService
   */
  protected async doValidate(): Promise<TValidationResult> {
    try {
      this.logOperation('doValidate', 'start');

      const errors: TValidationError[] = [];
      const warnings: TValidationWarning[] = [];

      // Validate utility health
      if (this._utilityErrors.length > 50) {
        warnings.push({
          code: 'HIGH_ERROR_COUNT',
          message: `High error count detected (${this._utilityErrors.length})`,
          severity: 'warning',
          component: 'TrackingUtilities',
          timestamp: new Date()
        });
      }

      // Validate performance cache
      if (this._performanceCache.size > 1000) {
        warnings.push({
          code: 'LARGE_CACHE_SIZE',
          message: `Performance cache size is large (${this._performanceCache.size})`,
          severity: 'info',
          component: 'TrackingUtilities',
          timestamp: new Date()
        });
      }

      const result: TValidationResult = {
        validationId: this.generateId(),
        componentId: 'TrackingUtilities',
        timestamp: new Date(),
        executionTime: 0,
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: errors.length === 0 ? (warnings.length === 0 ? 100 : 90) : 50,
        checks: [
          {
            checkId: 'utility-health-validation',
            name: 'Utility Health Validation',
            type: 'operational',
            status: this._utilityErrors.length <= 50 ? 'passed' : 'warning',
            score: this._utilityErrors.length <= 50 ? 100 : 80,
            details: `Error count: ${this._utilityErrors.length}`,
            timestamp: new Date()
          }
        ],
        references: {
          componentId: 'TrackingUtilities',
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: this._utilityOperationCounters.size,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: warnings.map(w => w.message),
        warnings: warnings.map(w => w.message),
        errors: errors.map(e => e.message),
        metadata: {
          validationMethod: 'tracking-utilities-validation',
          rulesApplied: errors.length + warnings.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

      this.logOperation('doValidate', 'complete', {
        status: result.status,
        errorsCount: errors.length,
        warningsCount: warnings.length
      });

      return result;

    } catch (error) {
      this.logError('doValidate', error);
      throw error;
    }
  }

  /**
   * Service-specific shutdown cleanup
   * ✅ MEMORY SAFETY: Uses doShutdown() lifecycle hook
   */
  protected async doShutdown(): Promise<void> {
    try {
      // Clear performance cache
      this._performanceCache.clear();

      // Clear operation counters
      this._utilityOperationCounters.clear();

      // Clear error tracking
      this._utilityErrors.length = 0;

      this.logOperation('doShutdown', 'Tracking utilities shutdown completed');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logError('doShutdown', error);
    }
  }

  // ============================================================================
  // INTERFACE IMPLEMENTATION
  // ============================================================================

  /**
   * Get utility name
   * @returns Utility name
   */
  public getUtilityName(): string {
    return this._utilityName;
  }

  /**
   * Execute utility operation
   * ✅ RESILIENT TIMING: Measures utility operation performance
   * @param operation - Operation to execute
   * @param params - Operation parameters
   * @returns Operation result
   */
  public async executeOperation(operation: string, params?: any): Promise<any> {
    const startTime = Date.now();
    const ctx = this._resilientTimer?.start();

    try {
      this._incrementUtilityCounter(operation);

      let result: any;
      
      switch (operation) {
        case 'validateConfig':
          result = await this.validateConfiguration(params);
          break;
        case 'mergeConfig':
          result = this.mergeConfiguration(params.base, params.override);
          break;
        case 'generateId':
          result = this.generateUniqueId(params?.prefix);
          break;
        case 'formatTimestamp':
          result = this.formatTimestamp(params?.date);
          break;
        case 'calculateMetrics':
          result = this.calculatePerformanceMetrics(params);
          break;
        case 'sanitizeData':
          result = this.sanitizeData(params);
          break;
        case 'validateInput':
          result = this.validateInput(params.data, params.rules);
          break;
        default:
          throw new Error(`Unknown operation: ${operation}`);
      }
      
      this._updatePerformanceCache(operation, Date.now() - startTime);
      return result;

    } catch (error) {
      this._trackUtilityError(operation, error);
      throw error;
    } finally {
      if (ctx) this._metricsCollector?.recordTiming('utility_operation', ctx.end());
    }
  }

  /**
   * Get available operations
   * @returns List of available operations
   */
  public getAvailableOperations(): string[] {
    return [
      'validateConfig',
      'mergeConfig',
      'generateId',
      'formatTimestamp',
      'calculateMetrics',
      'sanitizeData',
      'validateInput'
    ];
  }

  /**
   * Get helper name
   * @returns Helper name
   */
  public getHelperName(): string {
    return this._utilityName;
  }

  /**
   * Provide help for operation
   * @param operation - Operation to get help for
   * @returns Help information
   */
  public async getHelp(operation: string): Promise<any> {
    const helpInfo = {
      validateConfig: {
        description: 'Validate manager configuration',
        parameters: { config: 'TManagerConfig' },
        returns: 'TConfigValidation'
      },
      mergeConfig: {
        description: 'Merge configuration objects',
        parameters: { base: 'TManagerConfig', override: 'Partial<TManagerConfig>' },
        returns: 'TManagerConfig'
      },
      generateId: {
        description: 'Generate unique identifier',
        parameters: { prefix: 'string (optional)' },
        returns: 'string'
      },
      formatTimestamp: {
        description: 'Format timestamp to ISO string',
        parameters: { date: 'Date (optional)' },
        returns: 'string'
      },
      calculateMetrics: {
        description: 'Calculate performance metrics',
        parameters: { data: 'any' },
        returns: 'TManagerMetrics'
      },
      sanitizeData: {
        description: 'Sanitize input data',
        parameters: { data: 'any' },
        returns: 'any'
      },
      validateInput: {
        description: 'Validate input against rules',
        parameters: { data: 'any', rules: 'any' },
        returns: 'TConfigValidation'
      }
    };
    
    return helpInfo[operation as keyof typeof helpInfo] || null;
  }

  /**
   * Execute helper operation
   * @param operation - Operation to execute
   * @param context - Operation context
   * @returns Operation result
   */
  public async executeHelper(operation: string, context?: any): Promise<any> {
    return this.executeOperation(operation, context);
  }

  // ============================================================================
  // CONFIGURATION UTILITIES
  // ============================================================================

  /**
   * Validate manager configuration
   * ✅ RESILIENT TIMING: Measures configuration validation performance
   * @param config - Configuration to validate
   * @returns Validation result
   */
  public async validateConfiguration(config: any): Promise<TConfigValidation> {
    const ctx = this._resilientTimer?.start();
    const errors: Array<{ field: string; message: string; value?: any }> = [];
    const warnings: Array<{ field: string; message: string; value?: any }> = [];

    try {
      // Validate required fields
      for (const field of VALIDATION_RULES.REQUIRED_FIELDS) {
        if (!config[field]) {
          errors.push({
            field,
            message: `Required field '${field}' is missing`,
            value: config[field]
          });
        }
      }
      
      // Validate log level
      if (config.logLevel && !VALIDATION_RULES.ALLOWED_LOG_LEVELS.includes(config.logLevel)) {
        errors.push({
          field: 'logLevel',
          message: `Invalid log level. Must be one of: ${VALIDATION_RULES.ALLOWED_LOG_LEVELS.join(', ')}`,
          value: config.logLevel
        });
      }
      
      // Validate timeouts
      if (config.timeout) {
        for (const [key, value] of Object.entries(config.timeout)) {
          if (typeof value === 'number') {
            if (value < VALIDATION_RULES.MIN_TIMEOUT) {
              warnings.push({
                field: `timeout.${key}`,
                message: `Timeout value is below recommended minimum (${VALIDATION_RULES.MIN_TIMEOUT}ms)`,
                value
              });
            }
            if (value > VALIDATION_RULES.MAX_TIMEOUT) {
              warnings.push({
                field: `timeout.${key}`,
                message: `Timeout value is above recommended maximum (${VALIDATION_RULES.MAX_TIMEOUT}ms)`,
                value
              });
            }
          }
        }
      }
      
      // Validate monitoring configuration
      if (config.monitoring && config.monitoring.enabled) {
        if (!config.monitoring.interval || config.monitoring.interval < 1000) {
          warnings.push({
            field: 'monitoring.interval',
            message: 'Monitoring interval should be at least 1000ms for optimal performance',
            value: config.monitoring.interval
          });
        }
      }
      
      return {
        valid: errors.length === 0,
        errors,
        warnings,
        metadata: {
          timestamp: new Date().toISOString(),
          version: this._version
        }
      };
      
    } catch (error) {
      errors.push({
        field: 'general',
        message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
      
      return {
        valid: false,
        errors,
        warnings,
        metadata: {
          timestamp: new Date().toISOString(),
          version: this._version
        }
      };
    } finally {
      if (ctx) this._metricsCollector?.recordTiming('config_validation', ctx.end());
    }
  }

  /**
   * Merge configuration objects
   * @param base - Base configuration
   * @param override - Override configuration
   * @returns Merged configuration
   */
  public mergeConfiguration(base: TManagerConfig, override?: Partial<TManagerConfig>): TManagerConfig {
    if (!override) {
      return { ...base };
    }
    
    const merged: any = { ...base };
    
    // Merge primitive values
    for (const [key, value] of Object.entries(override)) {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object' && !Array.isArray(value)) {
          // Deep merge objects
          merged[key] = {
            ...(merged[key] || {}),
            ...value
          };
        } else {
          // Direct assignment for primitives and arrays
          merged[key] = value;
        }
      }
    }
    
    return merged as TManagerConfig;
  }

  /**
   * Get default configuration for manager type
   * @param managerType - Type of manager
   * @returns Default configuration
   */
  public getDefaultConfiguration(managerType: string): TManagerConfig {
    const baseConfig = { ...DEFAULT_MANAGER_CONFIG };
    
    // Customize based on manager type
    switch (managerType) {
      case 'tracking':
        baseConfig.id = 'tracking-manager';
        baseConfig.name = 'Tracking Manager';
        break;
      case 'file':
        baseConfig.id = 'file-manager';
        baseConfig.name = 'File Manager';
        break;
      case 'realtime':
        baseConfig.id = 'realtime-manager';
        baseConfig.name = 'Real Time Manager';
        break;
      case 'dashboard':
        baseConfig.id = 'dashboard-manager';
        baseConfig.name = 'Dashboard Manager';
        break;
      default:
        baseConfig.id = `${managerType}-manager`;
        baseConfig.name = `${managerType.charAt(0).toUpperCase()}${managerType.slice(1)} Manager`;
    }
    
    return baseConfig;
  }

  // ============================================================================
  // DATA UTILITIES
  // ============================================================================

  /**
   * Generate unique identifier
   * @param prefix - Optional prefix for the ID
   * @returns Unique identifier
   */
  public generateUniqueId(prefix?: string): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    const id = `${timestamp}-${random}`;
    
    return prefix ? `${prefix}-${id}` : id;
  }

  /**
   * Format timestamp to ISO string
   * @param date - Date to format (defaults to current date)
   * @returns Formatted timestamp
   */
  public formatTimestamp(date?: Date): string {
    return (date || new Date()).toISOString();
  }

  /**
   * Sanitize input data
   * ✅ RESILIENT TIMING: Measures data sanitization performance
   * @param data - Data to sanitize
   * @returns Sanitized data
   */
  public sanitizeData(data: any): any {
    const ctx = this._resilientTimer?.start();
    try {
      if (data === null || data === undefined) {
        return data;
      }
    
    if (typeof data === 'string') {
      // Trim whitespace and limit length
      return data.trim().substring(0, VALIDATION_RULES.MAX_STRING_LENGTH);
    }
    
    if (Array.isArray(data)) {
      // Limit array length and sanitize elements
      return data
        .slice(0, VALIDATION_RULES.MAX_ARRAY_LENGTH)
        .map(item => this.sanitizeData(item));
    }
    
    if (typeof data === 'object') {
      // Sanitize object properties
      const sanitized: any = {};
      let depth = 0;
      
      const sanitizeObject = (obj: any, currentDepth: number): any => {
        if (currentDepth >= VALIDATION_RULES.MAX_OBJECT_DEPTH) {
          return '[Object: Max depth exceeded]';
        }
        
        const result: any = {};
        for (const [key, value] of Object.entries(obj)) {
          if (typeof value === 'object' && value !== null) {
            result[key] = sanitizeObject(value, currentDepth + 1);
          } else {
            result[key] = this.sanitizeData(value);
          }
        }
        return result;
      };
      
      return sanitizeObject(data, 0);
    }

    return data;
    } finally {
      if (ctx) this._metricsCollector?.recordTiming('data_sanitization', ctx.end());
    }
  }

  /**
   * Validate input against rules
   * @param data - Data to validate
   * @param rules - Validation rules
   * @returns Validation result
   */
  public validateInput(data: any, rules: any): TConfigValidation {
    const errors: Array<{ field: string; message: string; value?: any }> = [];
    const warnings: Array<{ field: string; message: string; value?: any }> = [];
    
    try {
      // Basic validation
      if (rules.required && (data === null || data === undefined)) {
        errors.push({
          field: 'data',
          message: 'Required field is missing',
          value: data
        });
      }
      
      if (rules.type && typeof data !== rules.type) {
        errors.push({
          field: 'data',
          message: `Expected type ${rules.type}, got ${typeof data}`,
          value: data
        });
      }
      
      if (rules.minLength && typeof data === 'string' && data.length < rules.minLength) {
        errors.push({
          field: 'data',
          message: `Minimum length is ${rules.minLength}, got ${data.length}`,
          value: data
        });
      }
      
      if (rules.maxLength && typeof data === 'string' && data.length > rules.maxLength) {
        errors.push({
          field: 'data',
          message: `Maximum length is ${rules.maxLength}, got ${data.length}`,
          value: data
        });
      }
      
      return {
        valid: errors.length === 0,
        errors,
        warnings,
        metadata: {
          timestamp: new Date().toISOString(),
          version: this._version
        }
      };
      
    } catch (error) {
      return {
        valid: false,
        errors: [{
          field: 'validation',
          message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
        }],
        warnings,
        metadata: {
          timestamp: new Date().toISOString(),
          version: this._version
        }
      };
    }
  }

  // ============================================================================
  // PERFORMANCE UTILITIES
  // ============================================================================

  /**
   * Calculate performance metrics
   * @param data - Performance data
   * @returns Calculated metrics
   */
  public calculatePerformanceMetrics(data: any): any {
    try {
      const metrics = {
        timestamp: new Date().toISOString(),
        calculatedAt: Date.now(),
        performance: {
          avgResponseTime: this._calculateAverage(data.responseTimes || []),
          operationsPerSecond: this._calculateOperationsPerSecond(data.operations || [], data.timeWindow || 60000),
          memoryUsage: data.memoryUsage || 0,
          cpuUsage: data.cpuUsage || 0,
          errorRate: this._calculateErrorRate(data.totalOperations || 0, data.errors || 0)
        },
        thresholds: {
          responseTimeWarning: PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING,
          responseTimeCritical: PERFORMANCE_THRESHOLDS.RESPONSE_TIME_CRITICAL,
          errorRateWarning: PERFORMANCE_THRESHOLDS.ERROR_RATE_WARNING,
          errorRateCritical: PERFORMANCE_THRESHOLDS.ERROR_RATE_CRITICAL
        },
        status: this._calculatePerformanceStatus(data)
      };
      
      return metrics;
      
    } catch (error) {
      throw new Error(`Failed to calculate performance metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get performance statistics
   * @returns Performance statistics
   */
  public getPerformanceStatistics(): any {
    const stats = {
      operationCounts: Object.fromEntries(this._utilityOperationCounters),
      cacheHitRatio: this._calculateCacheHitRatio(),
      averageOperationTime: this._calculateAverageOperationTime(),
      errorCount: this._utilityErrors.length,
      recentErrors: this._utilityErrors.slice(-10),
      uptime: Date.now() - (this._performanceCache.get('startTime') || Date.now())
    };
    
    return stats;
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Increment operation counter
   * @param operation - Operation name
   */
  private _incrementUtilityCounter(operation: string): void {
    const current = this._utilityOperationCounters.get(operation) || 0;
    this._utilityOperationCounters.set(operation, current + 1);
  }

  /**
   * Update performance cache
   * @param operation - Operation name
   * @param duration - Operation duration
   */
  private _updatePerformanceCache(operation: string, duration: number): void {
    const key = `${operation}_times`;
    const times = this._performanceCache.get(key) || [];
    times.push(duration);
    
    // Keep only last 100 measurements
    if (times.length > 100) {
      times.shift();
    }
    
    this._performanceCache.set(key, times);
  }

  /**
   * Track error
   * @param operation - Operation name
   * @param error - Error object
   */
  private _trackUtilityError(operation: string, error: any): void {
    this._utilityErrors.push({
      timestamp: new Date().toISOString(),
      operation,
      error: error instanceof Error ? error.message : String(error)
    });

    // Keep only last 100 errors
    if (this._utilityErrors.length > 100) {
      this._utilityErrors.shift();
    }
  }

  /**
   * Calculate average from array of numbers
   * @param values - Array of numbers
   * @returns Average value
   */
  private _calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  /**
   * Calculate operations per second
   * @param operations - Array of operations
   * @param timeWindow - Time window in milliseconds
   * @returns Operations per second
   */
  private _calculateOperationsPerSecond(operations: any[], timeWindow: number): number {
    if (operations.length === 0) return 0;
    return (operations.length / timeWindow) * 1000;
  }

  /**
   * Calculate error rate
   * @param totalOperations - Total operations
   * @param errors - Number of errors
   * @returns Error rate percentage
   */
  private _calculateErrorRate(totalOperations: number, errors: number): number {
    if (totalOperations === 0) return 0;
    return (errors / totalOperations) * 100;
  }

  /**
   * Calculate performance status
   * @param data - Performance data
   * @returns Performance status
   */
  private _calculatePerformanceStatus(data: any): string {
    const avgResponseTime = this._calculateAverage(data.responseTimes || []);
    const errorRate = this._calculateErrorRate(data.totalOperations || 0, data.errors || 0);
    
    if (avgResponseTime > PERFORMANCE_THRESHOLDS.RESPONSE_TIME_CRITICAL || 
        errorRate > PERFORMANCE_THRESHOLDS.ERROR_RATE_CRITICAL) {
      return 'critical';
    }
    
    if (avgResponseTime > PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING || 
        errorRate > PERFORMANCE_THRESHOLDS.ERROR_RATE_WARNING) {
      return 'warning';
    }
    
    return 'healthy';
  }

  /**
   * Calculate cache hit ratio
   * @returns Cache hit ratio
   */
  private _calculateCacheHitRatio(): number {
    const hits = this._performanceCache.get('cache_hits') || 0;
    const misses = this._performanceCache.get('cache_misses') || 0;
    const total = hits + misses;
    
    return total === 0 ? 0 : hits / total;
  }

  /**
   * Calculate average operation time
   * @returns Average operation time
   */
  private _calculateAverageOperationTime(): number {
    const allTimes: number[] = [];
    
    for (const [key, times] of Array.from(this._performanceCache.entries())) {
      if (key.endsWith('_times') && Array.isArray(times)) {
        allTimes.push(...times);
      }
    }
    
    return this._calculateAverage(allTimes);
  }
} 