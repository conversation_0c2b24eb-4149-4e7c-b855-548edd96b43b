# Error Injection Techniques for Hard-to-Reach Code Paths

**Date**: 2025-08-28  
**Context**: GovernanceComplianceChecker.ts - 94%+ coverage achievement  
**Achievement**: Strategic error injection for comprehensive error handling validation  
**Focus**: Reaching catch blocks, error branches, and exception handling paths  

---

## 🎯 **OVERVIEW**

Hard-to-reach code paths in enterprise applications typically include:
- **Error handling branches** in try-catch blocks
- **Exception recovery logic** in complex operations
- **Fallback mechanisms** when dependencies fail
- **Validation error paths** for edge cases
- **Cleanup error handling** during shutdown operations

This lesson provides proven error injection techniques for achieving comprehensive coverage of these critical code paths.

---

## 🔧 **CORE ERROR INJECTION PATTERNS**

### **Pattern 1: Strategic Method Override for Error Injection**
```typescript
// ✅ TARGET: Error handling in complex operations
it('should handle validation errors gracefully', async () => {
  // Store original method
  const originalValidate = (instance as any)._performValidation;
  
  // Inject strategic error
  (instance as any)._performValidation = jest.fn().mockImplementation(() => {
    throw new Error('Validation service unavailable');
  });

  try {
    // Execute operation that should handle the error
    const result = await instance.validate();
    
    // Verify error handling behavior
    expect(result.status).toBe('error');
    expect(result.errors).toContain('Validation service unavailable');
  } finally {
    // ✅ CRITICAL: Always restore original method
    (instance as any)._performValidation = originalValidate;
  }
});
```

### **Pattern 2: Dependency Failure Simulation**
```typescript
// ✅ TARGET: Dependency failure handling
it('should handle timer coordination service failures', async () => {
  // Mock internal dependency to fail
  const mockTimerService = {
    createTimer: jest.fn().mockImplementation(() => {
      throw new Error('Timer service unavailable');
    }),
    cleanup: jest.fn()
  };

  // Replace dependency
  (instance as any)._timerCoordinationService = mockTimerService;

  try {
    // Execute operation that depends on timer service
    await instance.initialize();
    
    // Verify fallback behavior
    expect(instance.isHealthy()).toBe(true); // Should still be healthy
    expect((instance as any)._fallbackMode).toBe(true); // Should enable fallback
  } finally {
    // Restore original service
    await instance.shutdown();
  }
});
```

### **Pattern 3: Error Type Differentiation Testing**
```typescript
// ✅ TARGET: Different error type handling (Error vs non-Error objects)
it('should handle both Error and non-Error exceptions', async () => {
  const originalMethod = (instance as any)._processOperation;

  // Test 1: Standard Error object
  (instance as any)._processOperation = jest.fn().mockImplementation(() => {
    throw new Error('Standard error message');
  });

  let result = await instance.performOperation();
  expect(result.errorType).toBe('Error');
  expect(result.message).toBe('Standard error message');

  // Test 2: Non-Error object (string)
  (instance as any)._processOperation = jest.fn().mockImplementation(() => {
    throw 'String error message';
  });

  result = await instance.performOperation();
  expect(result.errorType).toBe('Unknown');
  expect(result.message).toBe('String error message');

  // Test 3: Non-Error object (object)
  (instance as any)._processOperation = jest.fn().mockImplementation(() => {
    throw { code: 'CUSTOM_ERROR', details: 'Custom error details' };
  });

  result = await instance.performOperation();
  expect(result.errorType).toBe('Object');
  expect(result.details).toBe('Custom error details');

  // Restore original method
  (instance as any)._processOperation = originalMethod;
});
```

---

## 🎯 **ADVANCED ERROR INJECTION TECHNIQUES**

### **Technique 1: Cascading Failure Simulation**
```typescript
// ✅ TARGET: Multiple failure points in sequence
it('should handle cascading system failures', async () => {
  // Simulate multiple component failures
  const failures = [
    { component: '_cacheManager', error: 'Cache service down' },
    { component: '_metricsCollector', error: 'Metrics service unavailable' },
    { component: '_alertManager', error: 'Alert service timeout' }
  ];

  const originalMethods: Record<string, any> = {};

  try {
    // Inject failures in all components
    failures.forEach(({ component, error }) => {
      originalMethods[component] = (instance as any)[component];
      (instance as any)[component] = {
        ...originalMethods[component],
        process: jest.fn().mockImplementation(() => {
          throw new Error(error);
        })
      };
    });

    // Execute operation that should handle all failures
    const result = await instance.performComprehensiveOperation();

    // Verify graceful degradation
    expect(result.status).toBe('degraded');
    expect(result.failedComponents).toHaveLength(3);
    expect(result.operationalComponents).toContain('core');
  } finally {
    // Restore all original methods
    Object.entries(originalMethods).forEach(([component, original]) => {
      (instance as any)[component] = original;
    });
  }
});
```

### **Technique 2: Timing-Based Error Injection**
```typescript
// ✅ TARGET: Error handling during specific operation phases
it('should handle errors during different operation phases', async () => {
  let callCount = 0;
  const originalMethod = (instance as any)._executePhase;

  (instance as any)._executePhase = jest.fn().mockImplementation((phase: string) => {
    callCount++;
    
    // Inject error on specific call (e.g., third phase)
    if (callCount === 3) {
      throw new Error(`Phase ${phase} execution failed`);
    }
    
    return originalMethod.call(instance, phase);
  });

  try {
    const result = await instance.executeMultiPhaseOperation();
    
    // Verify partial completion and error recovery
    expect(result.completedPhases).toBe(2);
    expect(result.failedPhase).toBe('phase-3');
    expect(result.recoveryAction).toBe('rollback-initiated');
  } finally {
    (instance as any)._executePhase = originalMethod;
  }
});
```

### **Technique 3: Resource Exhaustion Simulation**
```typescript
// ✅ TARGET: Resource limit error handling
it('should handle resource exhaustion gracefully', async () => {
  // Simulate memory/resource exhaustion
  const originalCreateResource = (instance as any)._createResource;
  let resourceCount = 0;

  (instance as any)._createResource = jest.fn().mockImplementation(() => {
    resourceCount++;
    
    // Simulate resource exhaustion after 5 resources
    if (resourceCount > 5) {
      throw new Error('ENOMEM: Not enough memory');
    }
    
    return originalCreateResource.call(instance);
  });

  try {
    // Attempt to create many resources
    const results = [];
    for (let i = 0; i < 10; i++) {
      try {
        const resource = await instance.createManagedResource();
        results.push(resource);
      } catch (error) {
        // Should handle resource exhaustion
        expect(error.message).toContain('memory');
        break;
      }
    }

    // Verify resource management
    expect(results).toHaveLength(5); // Only 5 should succeed
    expect(instance.getResourceCount()).toBe(5);
    expect(instance.isResourceLimitReached()).toBe(true);
  } finally {
    (instance as any)._createResource = originalCreateResource;
  }
});
```

---

## 🛡️ **ERROR INJECTION SAFETY PATTERNS**

### **Pattern 1: Safe Method Restoration**
```typescript
// ✅ SAFE: Always restore original methods
class ErrorInjectionHelper {
  private originalMethods: Map<string, any> = new Map();

  injectError(instance: any, methodName: string, errorFactory: () => Error): void {
    // Store original method
    this.originalMethods.set(methodName, instance[methodName]);
    
    // Inject error
    instance[methodName] = jest.fn().mockImplementation(() => {
      throw errorFactory();
    });
  }

  restoreAll(instance: any): void {
    // Restore all modified methods
    this.originalMethods.forEach((originalMethod, methodName) => {
      instance[methodName] = originalMethod;
    });
    this.originalMethods.clear();
  }
}

// Usage in tests
it('should handle multiple error scenarios safely', async () => {
  const errorHelper = new ErrorInjectionHelper();

  try {
    errorHelper.injectError(instance, '_validateInput', () => new Error('Validation failed'));
    errorHelper.injectError(instance, '_processData', () => new Error('Processing failed'));

    const result = await instance.performComplexOperation();
    expect(result.errors).toHaveLength(2);
  } finally {
    errorHelper.restoreAll(instance);
  }
});
```

### **Pattern 2: Conditional Error Injection**
```typescript
// ✅ CONDITIONAL: Error injection based on test conditions
it('should handle conditional error scenarios', async () => {
  let shouldFail = false;
  const originalMethod = (instance as any)._criticalOperation;

  (instance as any)._criticalOperation = jest.fn().mockImplementation((...args) => {
    if (shouldFail) {
      throw new Error('Conditional failure triggered');
    }
    return originalMethod.apply(instance, args);
  });

  try {
    // First execution should succeed
    shouldFail = false;
    let result = await instance.executeOperation();
    expect(result.success).toBe(true);

    // Second execution should fail
    shouldFail = true;
    result = await instance.executeOperation();
    expect(result.success).toBe(false);
    expect(result.error).toContain('Conditional failure');
  } finally {
    (instance as any)._criticalOperation = originalMethod;
  }
});
```

---

## 📊 **ERROR PATH COVERAGE STRATEGIES**

### **Strategy 1: Systematic Error Path Mapping**
```typescript
// ✅ SYSTEMATIC: Map all error paths in complex methods
describe('Comprehensive Error Path Coverage', () => {
  const errorScenarios = [
    {
      name: 'validation failure',
      method: '_validateInput',
      error: new Error('Invalid input format'),
      expectedBehavior: 'return validation error result'
    },
    {
      name: 'processing timeout',
      method: '_processWithTimeout',
      error: new Error('Operation timeout'),
      expectedBehavior: 'trigger timeout recovery'
    },
    {
      name: 'storage failure',
      method: '_persistResults',
      error: new Error('Storage unavailable'),
      expectedBehavior: 'enable temporary storage mode'
    }
  ];

  errorScenarios.forEach(({ name, method, error, expectedBehavior }) => {
    it(`should handle ${name} correctly`, async () => {
      const originalMethod = (instance as any)[method];
      
      (instance as any)[method] = jest.fn().mockImplementation(() => {
        throw error;
      });

      try {
        const result = await instance.performOperation();
        
        // Verify expected error handling behavior
        expect(result.errorHandled).toBe(true);
        expect(result.errorType).toBe(name.replace(' ', '_'));
        
        // Add specific assertions based on expectedBehavior
        if (expectedBehavior.includes('validation error')) {
          expect(result.validationErrors).toBeDefined();
        } else if (expectedBehavior.includes('timeout recovery')) {
          expect(result.timeoutRecovery).toBe(true);
        } else if (expectedBehavior.includes('temporary storage')) {
          expect(result.temporaryMode).toBe(true);
        }
      } finally {
        (instance as any)[method] = originalMethod;
      }
    });
  });
});
```

### **Strategy 2: Error Recovery Validation**
```typescript
// ✅ RECOVERY: Validate error recovery mechanisms
it('should demonstrate complete error recovery cycle', async () => {
  const originalMethods = {
    process: (instance as any)._processData,
    recover: (instance as any)._recoverFromError,
    validate: (instance as any)._validateRecovery
  };

  let errorInjected = false;
  let recoveryAttempted = false;

  try {
    // Inject error in processing
    (instance as any)._processData = jest.fn().mockImplementation(() => {
      if (!errorInjected) {
        errorInjected = true;
        throw new Error('Processing failed - triggering recovery');
      }
      return 'processed successfully after recovery';
    });

    // Monitor recovery attempt
    (instance as any)._recoverFromError = jest.fn().mockImplementation(() => {
      recoveryAttempted = true;
      return originalMethods.recover.call(instance);
    });

    // Execute operation that should trigger error and recovery
    const result = await instance.performResilientOperation();

    // Verify complete error recovery cycle
    expect(errorInjected).toBe(true);
    expect(recoveryAttempted).toBe(true);
    expect(result.success).toBe(true);
    expect(result.recoveryPerformed).toBe(true);
    expect(result.finalResult).toBe('processed successfully after recovery');
  } finally {
    // Restore all methods
    Object.entries(originalMethods).forEach(([key, method]) => {
      (instance as any)[`_${key}`] = method;
    });
  }
});
```

---

## 🎯 **COVERAGE VERIFICATION TECHNIQUES**

### **Technique 1: Error Path Coverage Validation**
```bash
# Generate coverage report focusing on error paths
npm test -- --coverage --testPathPattern="target.test.ts" --collectCoverageFrom="**/target.ts"

# Analyze uncovered lines in error handling
cat coverage/lcov.info | grep -A 5 -B 5 "catch\|error\|exception"
```

### **Technique 2: Branch Coverage Analysis**
```typescript
// ✅ VERIFY: All error handling branches covered
it('should achieve complete branch coverage in error handling', async () => {
  const errorTypes = ['Error', 'string', 'object', 'null', 'undefined'];
  
  for (const errorType of errorTypes) {
    const originalMethod = (instance as any)._processWithErrorHandling;
    
    (instance as any)._processWithErrorHandling = jest.fn().mockImplementation(() => {
      switch (errorType) {
        case 'Error':
          throw new Error('Standard error');
        case 'string':
          throw 'String error';
        case 'object':
          throw { code: 'CUSTOM', message: 'Object error' };
        case 'null':
          throw null;
        case 'undefined':
          throw undefined;
      }
    });

    try {
      const result = await instance.performOperation();
      expect(result.errorType).toBe(errorType);
    } finally {
      (instance as any)._processWithErrorHandling = originalMethod;
    }
  }
});
```

---

## 🔗 **INTEGRATION WITH SURGICAL PRECISION TESTING**

### **Combined Pattern: Error Injection + Private Method Access**
```typescript
// ✅ COMBINED: Error injection with surgical precision
it('should test private error handling methods directly', async () => {
  // Access private error handling method
  const privateErrorHandler = (instance as any)._handleProcessingError.bind(instance);
  
  // Test different error scenarios
  const errorScenarios = [
    new Error('Standard error'),
    'String error',
    { code: 'CUSTOM_ERROR', details: 'Custom error object' },
    null,
    undefined
  ];

  for (const error of errorScenarios) {
    const result = await privateErrorHandler(error);
    
    // Verify error handling logic
    expect(result).toHaveProperty('handled', true);
    expect(result).toHaveProperty('errorType');
    expect(result).toHaveProperty('recoveryAction');
  }
});
```

---

**This comprehensive error injection methodology enables thorough testing of error handling paths while maintaining type safety and preserving all surgical precision testing patterns necessary for achieving high coverage in complex enterprise applications.**
